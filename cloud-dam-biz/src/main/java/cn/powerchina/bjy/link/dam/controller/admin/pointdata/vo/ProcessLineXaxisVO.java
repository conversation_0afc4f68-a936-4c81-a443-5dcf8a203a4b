package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 监测图形-过程线/分布图的响应--x轴
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-过程线/分布图-x轴")
@Data
public class XaxisVO {

    private String type;

    private Boolean boundaryGap;

    private AxisLabelVO axisLabel;

    private List<BigDecimal> data;

    private AxisLineVO axisLine;

    private String position;
}

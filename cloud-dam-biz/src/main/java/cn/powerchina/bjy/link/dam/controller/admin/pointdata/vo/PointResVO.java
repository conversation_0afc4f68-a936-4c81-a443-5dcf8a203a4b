package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 监测图形-分布图-测点信息
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图-测点信息")
@Data
@ToString(callSuper = true)
public class PointResVO {

    @Schema(description = "读取日期")
    private LocalDateTime pointTime;

    @Schema(description = "测点值")
    private List<PointDataResVO> pointDateList;
}

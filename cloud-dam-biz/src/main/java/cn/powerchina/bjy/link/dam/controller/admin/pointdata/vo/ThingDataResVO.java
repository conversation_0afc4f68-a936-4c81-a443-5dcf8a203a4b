package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description: 监测图形-过程线-分量信息-分量值
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线-分量信息-分量值")
@Data
public class ThingDataResVO {

    @Schema(description = "分量值")
    private BigDecimal thingValue;

    @Schema(description = "测值时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime pointTime;
}
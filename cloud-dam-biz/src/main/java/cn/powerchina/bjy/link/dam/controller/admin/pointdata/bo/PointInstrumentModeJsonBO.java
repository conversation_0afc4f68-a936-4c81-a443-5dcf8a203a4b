package cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 分量数据json
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/27
 */
@Data
public class PointInstrumentModeJsonBO {

    /**
     * 分量id
     */
    private Long instrumentModelId;

    /**
     * 分量名称
     */
    private String thingName;

    /**
     * 分量标识符
     */
    private String thingIdentity;

    /**
     * 分量值
     */
    private BigDecimal thingValue;
}

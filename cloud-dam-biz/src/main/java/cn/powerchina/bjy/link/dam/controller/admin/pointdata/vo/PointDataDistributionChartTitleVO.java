package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 监测图形-分布图--标题
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-标题")
@Data
public class PointDataDistributionChartTitleVO {
    private String text;

}

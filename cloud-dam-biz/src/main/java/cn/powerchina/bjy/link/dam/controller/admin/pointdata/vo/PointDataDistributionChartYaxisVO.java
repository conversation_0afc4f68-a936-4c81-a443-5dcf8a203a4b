package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-分布图--y轴
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-y轴")
@Data
public class PointDataDistributionChartYaxisVO {

    private String type;

    private String name;

    private String position;

    private Boolean alignTicks;

    private PointDataDistributionChartYaxisLineVO axisLine;

    private PointDataDistributionChartAxisTickVO axisTick;
}

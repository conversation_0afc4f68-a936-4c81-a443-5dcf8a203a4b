package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-过程线-分量信息
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线-分量信息")
@Data
public class ThingResVO {

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "分量单位")
    private String thingUnit;

    @Schema(description = "分量值")
    private List<ThingDataResVO> thingDataList;
}
package cn.powerchina.bjy.link.dam.service.pointdata;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 测点数据 Service 接口
 *
 * <AUTHOR>
 */
public interface PointDataService {

    /**
     * 创建测点数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointData(@Valid PointDataSaveReqVO createReqVO);

    /**
     * 更新测点数据
     *
     * @param updateReqVO 更新信息
     */
    void updatePointData(@Valid PointDataSaveReqVO updateReqVO);

    /**
     * 删除测点数据
     *
     * @param id 编号
     */
    void deletePointData(Long id);

    /**
     * 获得测点数据
     *
     * @param id 编号
     * @return 测点数据
     */
    PointDataDO getPointData(Long id);

    /**
     * 获得测点数据分页
     *
     * @param pageReqVO 分页查询
     * @return 测点数据分页
     */
    PageResult<PointDataDO> getPointDataPage(PointDataPageReqVO pageReqVO);

    /**
     * 根据设备上报的数据批量保存测点数据
     *
     * @param deviceDataTransportModel
     */
    void saveBatchPointData(DeviceDataTransportModel deviceDataTransportModel);

    /**
     * 获得测点数据分页
     *
     * @param pageReqVO
     * @return
     */
    PageResult<PointDataBO> getPointDataBOPage(PointDataPageReqVO pageReqVO);

    /**
     * 获得测点数据分页
     *   (优化版)
     * @param pageReqVO
     * @return
     */
    PageResult<PointDataBO> pointStatisticsPage(PointDataPageReqVO pageReqVO);


    List<PointDataBO> pointStatistics(PointDataStatisticsVO statisticsVO);


    /**
     * 重新计算
     *
     * @param pointDataJson
     */
    void recalculate(PointDataJsonDO pointDataJson);

    /**
     * 根据测点查询数量
     *
     * @param projectId
     * @param pointId
     * @return
     */
    Long countByProjectIdAndPointId(Long projectId, Long pointId);

    /**
     * 根据仪器类型分量组织测点数据
     *
     * @param instrumentModelDO
     * @param pointId
     * @param localDateTime
     * @param value
     * @return
     */
    PointDataDO createPointData(InstrumentModelDO instrumentModelDO, Long pointId, LocalDateTime localDateTime, BigDecimal value, Integer dataType);

    /**
     * 批量保存
     *
     * @param pointDataBOList
     */
    void insertBatch(List<PointDataDO> pointDataBOList);

    /**
     * 删除测点某时刻的人工数据
     *
     * @param pointId
     * @param pointTime
     */
    void deleteByPointIdAndPointTime(Long pointId, LocalDateTime pointTime);

    /**
     * 查询时间范围内的数据
     *
     * @param pointId
     * @param startTime
     * @param endTime
     */
    List<PointDataDO> selectByPointIdAndTime(Long pointId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType);


    PointDataDO getExtremaPointData(Long pointId,Long instrumentModelId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType,boolean isMax);

    /**
     * 计算测点下的成果值
     *
     * @param pointId
     * @param localDateTime
     * @param pointModelMap
     * @param dataType
     * @return
     */
    List<PointDataDO> calculatePointFormula(Long pointId, LocalDateTime localDateTime, Map<String, Object> pointModelMap, Integer dataType);

    /**
     * 测点数据导出
     *
     * @param response
     * @param
     */
    void exportExcel(HttpServletResponse response, PointDataStatisticsVO statisticsVO);

    /**
     * 测点数据折线图：分量数据结果
     *
     * @param pointDataReqVO
     * @return
     */
    PointDataModelRespVO getModelValueList(PointDataReqVO pointDataReqVO);

    /**
     * 获取测点数据过程线
     *
     * @param pointDataProcessLineReqVO
     * @return
     */
    List<PointDataProcessLineResVO> listPointDataProcessLine(PointDataProcessLineReqVO pointDataProcessLineReqVO);

    /**
     * 获取测点数据分布图
     *
     * @param pointDataDistributionChartReqVO
     * @return
     */
    List<PointDataDistributionChartResVO> listPointDataDistributionChart(PointDataDistributionChartReqVO pointDataDistributionChartReqVO);
}
package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 监测图形-过程线的响应
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线 Response VO")
@Data
public class PointDataProcessLineResVO {

    @Schema(description = "测点名称")
    private String pointName;

    @Schema(description = "分量列表")
    private List<ThingResVO> thingList;
}
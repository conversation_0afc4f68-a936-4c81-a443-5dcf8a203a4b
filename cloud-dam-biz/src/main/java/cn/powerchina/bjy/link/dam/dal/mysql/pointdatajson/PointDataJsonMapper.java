package cn.powerchina.bjy.link.dam.dal.mysql.pointdatajson;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataJsonPointTimeBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 测点数据json Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointDataJsonMapper extends BaseMapperX<PointDataJsonDO> {

    default PageResult<PointDataJsonRespVO> selectPage(PointDataJsonPageReqVO reqVO) {
        MPJLambdaWrapperX<PointDataJsonDO> pointDataJsonDOMPJLambdaWrapperX = new MPJLambdaWrapperX<PointDataJsonDO>()
                .eqIfPresent(PointDataJsonDO::getPointId, reqVO.getPointId())
                .betweenIfPresent(PointDataJsonDO::getPointTime, reqVO.getPointTime())
                .eqIfPresent(PointDataJsonDO::getDataType, reqVO.getDataType());
        //审核状态
        if (CollectionUtils.isNotEmpty(reqVO.getReviewStatusList())) {
            pointDataJsonDOMPJLambdaWrapperX.in(PointDataJsonDO::getReviewStatus, reqVO.getReviewStatusList());
        }
        //数据状态
        if (reqVO.getDataStatusList() != null) {
            pointDataJsonDOMPJLambdaWrapperX.in(PointDataJsonDO::getDataStatus, reqVO.getDataStatusList());
        }
        pointDataJsonDOMPJLambdaWrapperX.orderByDesc(PointDataJsonDO::getPointTime);
        return selectJoinPage(reqVO, PointDataJsonRespVO.class, pointDataJsonDOMPJLambdaWrapperX);
    }

    /**
     * 根据一批测点id查询最大检测时间
     * @param pointIdList
     * @return
     */
    List<PointDataJsonDO> listMaxPointTimeByPointIdList(@Param("pointIdList") List<Long> pointIdList);

    /**
     * 查询检测图形-过程线的数据
     * @param projectId
     * @param pointTimeList
     * @param dataTypeList
     * @param dataStatusList
     * @param reviewStatusList
     * @return
     */
    List<PointDataJsonDO> list4ProcessLine(@Param("projectId") Long projectId,
                                           @Param("pointTimeList") List<PointDataJsonPointTimeBO> pointTimeList,
                                           @Param("dataTypeList") List<Integer> dataTypeList,
                                           @Param("dataStatusList") List<Integer> dataStatusList,
                                           @Param("reviewStatusList") List<Integer> reviewStatusList);

    /**
     * 批量逻辑删除
     *
     * @param pointId
     * @param startTime
     * @param endTime
     * @param dataType
     */
    void batchUpdateDelete(@Param("pointId") Long pointId,
                           @Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime,
                           @Param("dataType") Integer dataType,
                           @Param("rollbackImportId") Long rollbackImportId);

    /**
     * 批量恢复
     *
     * @param pointId
     * @param startTime
     * @param endTime
     * @param dataType
     */
    void batchUpdateRecover(@Param("pointId") Long pointId,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime,
                            @Param("dataType") Integer dataType,
                            @Param("rollbackImportId") Long rollbackImportId);

}
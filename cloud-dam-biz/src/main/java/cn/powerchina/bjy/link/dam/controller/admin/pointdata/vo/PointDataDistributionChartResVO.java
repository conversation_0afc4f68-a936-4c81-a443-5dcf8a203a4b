package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @Description: 监测图形-分布图的响应
 * @Author: yangjingtao
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-过程线 Response VO")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartResVO {

    @Schema(description = "分量id")
    private Long instrumentModelId;

    @Schema(description = "分量名称")
    private String thingName;

    @Schema(description = "分量单位")
    private String thingUnit;

    @Schema(description = "测点信息")
    private List<PointResVO> pointList;

}

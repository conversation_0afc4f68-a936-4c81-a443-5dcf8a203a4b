package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图的响应
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图 Response VO")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartResVO {

    private PointDataDistributionChartTitleVO title;

    private PointDataDistributionChartTootipVO tootip;

    private PointDataDistributionChartLegendVO legend;

    private PointDataDistributionChartXaxisVO xAxis;

    private PointDataDistributionChartYaxisVO yAxis;

    private List<PointDataDistributionChartSeriesVO> series;
}

package cn.powerchina.bjy.link.dam.service.pointdata;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.dict.DictDataApi;
import cn.powerchina.bjy.cloud.system.api.dict.dto.DictDataRespDTO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.bo.PointInstrumentModelBO;
import cn.powerchina.bjy.link.dam.controller.admin.point.vo.PointInstrumentPageReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointalarm.vo.PointAlarmSaveReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataJsonBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo.PointDataJsonPointTimeBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.PointInstrumentModelJsonVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulamodel.FormulaModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.formulapoint.FormulaPointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrument.InstrumentDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointevaluate.PointEvaluateDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointformula.PointFormulaDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointparam.PointParamDO;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdata.PointDataMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.pointdatajson.PointDataJsonMapper;
import cn.powerchina.bjy.link.dam.enums.*;
import cn.powerchina.bjy.link.dam.service.device.DeviceService;
import cn.powerchina.bjy.link.dam.service.formulamodel.FormulaModelService;
import cn.powerchina.bjy.link.dam.service.formulapoint.FormulaPointService;
import cn.powerchina.bjy.link.dam.service.instrument.InstrumentService;
import cn.powerchina.bjy.link.dam.service.instrumentmodel.InstrumentModelService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.pointalarm.PointAlarmService;
import cn.powerchina.bjy.link.dam.service.pointdatajson.PointDataJsonService;
import cn.powerchina.bjy.link.dam.service.pointevaluate.PointEvaluateService;
import cn.powerchina.bjy.link.dam.service.pointformula.PointFormulaService;
import cn.powerchina.bjy.link.dam.service.pointparam.PointParamService;
import cn.powerchina.bjy.link.dam.util.AviatorUtils;
import cn.powerchina.bjy.link.dam.util.BigDecimalUtils;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.awt.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ApiConstants.DICT_FORMULA;
import static cn.powerchina.bjy.link.dam.enums.DamConstant.MAINBOARD_VOLTAGE;
import static cn.powerchina.bjy.link.dam.enums.DamConstant.RELATIVE_DATA_BEFORE;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.*;

/**
 * 测点数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PointDataServiceImpl implements PointDataService {

    @Resource
    private PointDataMapper pointDataMapper;

    @Resource
    private DeviceService deviceService;

    @Resource
    private PointService pointService;

    @Resource
    private InstrumentService instrumentService;

    @Resource
    private InstrumentModelService instrumentModelService;

    @Resource
    private PointEvaluateService pointEvaluateService;

    @Resource
    private PointFormulaService pointFormulaService;

    @Resource
    private FormulaModelService formulaModelService;

    @Resource
    private FormulaPointService formulaPointService;

    @Resource
    private PointDataJsonService pointDataJsonService;

    @Resource
    private PointDataJsonMapper pointDataJsonMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private PointParamService pointParamService;

    @Resource
    private PointAlarmService pointAlarmService;

    @Resource
    private ThreadPoolTaskExecutor damThreadPoolTaskExecutor;

    @Override
    public Long createPointData(PointDataSaveReqVO createReqVO) {
        // 插入
        PointDataDO pointData = BeanUtils.toBean(createReqVO, PointDataDO.class);
        pointDataMapper.insert(pointData);
        // 返回
        return pointData.getId();
    }

    @Override
    public void updatePointData(PointDataSaveReqVO updateReqVO) {
        // 校验存在
        validatePointDataExists(updateReqVO.getId());
        // 更新
        PointDataDO updateObj = BeanUtils.toBean(updateReqVO, PointDataDO.class);
        pointDataMapper.updateById(updateObj);
    }

    @Override
    public void deletePointData(Long id) {
        // 校验存在
        validatePointDataExists(id);
        // 删除
        pointDataMapper.deleteById(id);
    }

    private void validatePointDataExists(Long id) {
        if (pointDataMapper.selectById(id) == null) {
            throw exception(POINT_DATA_NOT_EXISTS);
        }
    }

    @Override
    public PointDataDO getPointData(Long id) {
        return pointDataMapper.selectById(id);
    }

    @Override
    public PageResult<PointDataDO> getPointDataPage(PointDataPageReqVO pageReqVO) {
        return pointDataMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional
    public void saveBatchPointData(DeviceDataTransportModel deviceDataTransportModel) {
        //可以修改没有绑定测点的设备电压
        if (!CollectionUtils.isEmpty(deviceDataTransportModel.getDeviceDataList())) {
            deviceDataTransportModel.getDeviceDataList().forEach(item -> {
                //根据上报的电压修改设备电压属性
                if (MAINBOARD_VOLTAGE.equalsIgnoreCase(item.getThingIdentity())) {
                    deviceService.updateDeviceVoltage(deviceDataTransportModel.getDeviceCode(), item.getThingValue());
                }
            });
        }
        //根据设备编码找到大坝设备
        DeviceDO deviceDOByDeviceCode = deviceService.getDeviceDOByDeviceCodeAndMcuChannel(deviceDataTransportModel.getDeviceCode(), deviceDataTransportModel.getMcuChannel());
        if (Objects.isNull(deviceDOByDeviceCode)) {
            log.warn("deviceCode {}-{} not exist or not bind point in dam", deviceDataTransportModel.getDeviceCode(), deviceDataTransportModel.getMcuChannel());
            return;
        }
        //找到设备绑定的测点
        Long pointId = deviceDOByDeviceCode.getPointId();
        PointBO pointBO = pointService.getPointBO(pointId);
        if (Objects.isNull(pointBO)) {
            log.warn("deviceCode {} bind pointId {} not exist", deviceDataTransportModel.getDeviceCode(), pointId);
            return;
        }
        //采集时间
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(deviceDataTransportModel.getCurrentTime()), ZoneId.systemDefault());

        //测点分量集合
        Map<String, Object> pointModelMap = new HashMap<>();
        List<PointDataDO> pointDataDOList = new ArrayList<>();
        deviceDataTransportModel.getDeviceDataList().forEach(item -> {
            //上报的原始值
            BigDecimal value = new BigDecimal(item.getThingValue());
            //根据物模型标识符查找仪器类型的测量分量
            InstrumentModelDO instrumentModelDO = instrumentModelService.getModelByInstrumentIdAndThingIdentityIot(pointBO.getInstrumentId(), item.getThingIdentity());
            if (Objects.nonNull(instrumentModelDO)) {
                PointDataDO pointData = createPointData(instrumentModelDO, pointId, localDateTime, value, DataTypeEnum.AUTO_COLLECTION.getType());
                pointDataDOList.add(pointData);
                pointModelMap.put(pointData.getThingIdentity(), value);
            } else {
                log.warn("deviceCode {} iot identity {} not bind InstrumentModel", deviceDataTransportModel.getDeviceCode(), item.getThingIdentity());
            }
        });
        List<PointDataDO> resultPointDataDOList = calculatePointFormula(pointId, localDateTime, pointModelMap, DataTypeEnum.AUTO_COLLECTION.getType());
        pointDataDOList.addAll(resultPointDataDOList);
        //批量插入测点数据
        pointDataMapper.insertBatch(pointDataDOList);
        if (CollectionUtil.isNotEmpty(pointDataDOList)) {
            //保存测点数据json
            PointDataJsonDO jsonSaveReqVO = new PointDataJsonDO();
            BeanUtil.copyProperties(pointDataDOList.get(0), jsonSaveReqVO, "id");
            jsonSaveReqVO.setPointData(JSONObject.toJSONString(BeanUtils.toBean(pointDataDOList, PointInstrumentModelJsonVO.class)));
            //状态
            pointDataJsonService.setPointJsonStatus(pointDataDOList, jsonSaveReqVO);
            pointDataJsonMapper.insert(jsonSaveReqVO);
        }
    }

    @Override
    @Transactional
    public void recalculate(PointDataJsonDO pointDataJson) {
        List<PointInstrumentModelJsonVO> modelJsonVOS = JSONObject.parseArray(pointDataJson.getPointData(), PointInstrumentModelJsonVO.class);
        Map<String, Object> pointModelMap = modelJsonVOS.stream().collect(HashMap::new, (map, item) -> map.put(item.getThingIdentity(), item.getThingValue()), HashMap::putAll
        );
        LocalDateTime localDateTime = pointDataJson.getPointTime();
        List<PointDataDO> pointDataDOList = calculatePointFormula(pointDataJson.getPointId(), localDateTime, pointModelMap, pointDataJson.getDataType());
        Map<Long, BigDecimal> resultValueMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(pointDataDOList)) {
            pointDataDOList.forEach(item -> {
                resultValueMap.put(item.getInstrumentModelId(), item.getThingValue());
            });
        }
        //所有中间值和成果值都要显示计算结果
        List<InstrumentModelDO> instrumentModelList = instrumentModelService.getInstrumentModelList(pointDataJson.getPointId());
        List<InstrumentModelDO> modelDOList = instrumentModelList.stream().filter(item -> !item.getThingType().equals(InstrumentThingTypeEnum.ORIGIN.getType())).toList();
        for (InstrumentModelDO model : modelDOList) {
            if (!resultValueMap.containsKey(model.getId())) {
                throw exception(POINT_DATA_MODEL_NO_FORMULA, model.getThingName());
            }
            if (Objects.isNull(resultValueMap.get(model.getId()))) {
                throw exception(POINT_DATA_MODEL_FORMULA_ERROR, model.getThingName());
            }
        }
        //批量插入测点数据的中间值和成果值
        pointDataMapper.insertBatch(pointDataDOList);

        modelJsonVOS.addAll(BeanUtils.toBean(pointDataDOList, PointInstrumentModelJsonVO.class));
        //最新的结果分量替换旧的结果分量
        Map<String, PointInstrumentModelJsonVO> map = new LinkedHashMap<>();
        for (PointInstrumentModelJsonVO model : modelJsonVOS) {
            map.put(model.getThingIdentity(), model);
        }
        //修改测量数据json
        pointDataJson.setPointData(JSONObject.toJSONString(new ArrayList<>(map.values())));
        //如果计算完成的分量有异常数据，状态设置为异常
        pointDataJsonService.setPointJsonStatus(pointDataDOList, pointDataJson);
        pointDataJsonMapper.updateById(pointDataJson);
    }

    @Override
    public Long countByProjectIdAndPointId(Long projectId, Long pointId) {
        Long count = pointDataMapper.selectCount(new LambdaQueryWrapperX<PointDataDO>()
                .eq(PointDataDO::getProjectId, projectId)
                .eq(PointDataDO::getPointId, pointId));
        return Objects.nonNull(count) ? count : 0L;
    }

    /**
     * 计算测点下所有公式
     *
     * @param pointId
     * @param localDateTime
     * @param pointModelMap
     */
    public List<PointDataDO> calculatePointFormula(Long pointId, LocalDateTime localDateTime, Map<String, Object> pointModelMap, Integer dataType) {
        //中间值和成果值
        List<PointDataDO> pointDataDOList = new ArrayList<>();
        List<Integer> applyTypeList = getApplyTypeByData(dataType);
        //查询测点计算公式:适用类型是全部和自动化的，且在生效时间内
        List<PointFormulaDO> allPointFormulaDOList = pointFormulaService.getPointFormulaByPointId(pointId)
                .stream().filter(pointFormulaDO -> applyTypeList.contains(pointFormulaDO.getApplyType())).toList();
        // 按照modelId分组
        Map<Long, List<PointFormulaDO>> groupedByModelIdMap = allPointFormulaDOList.stream()
                .collect(Collectors.groupingBy(PointFormulaDO::getInstrumentModelId));
        groupedByModelIdMap.forEach((modelId, pointFormulaDOList) -> {
            //生效的测点公式
            PointFormulaDO pointFormula = null;
            //生效时间为空，所有时间都生效
            List<PointFormulaDO> nullPointFormulaList = pointFormulaDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
            if (CollectionUtil.isEmpty(nullPointFormulaList)) {
                //判断是否在生效时间内
                pointFormulaDOList = pointFormulaDOList.stream().filter(pointFormulaDO -> pointFormulaDO.getEffectiveStartTime().isBefore(localDateTime) && pointFormulaDO.getEffectiveEndTime().isAfter(localDateTime))
                        .toList();
                if (CollectionUtil.isNotEmpty(pointFormulaDOList)) {
                    pointFormula = pointFormulaDOList.get(0);
                }
            } else {
                pointFormula = nullPointFormulaList.get(0);
            }

            if (Objects.nonNull(pointFormula)) {
                PointDataDO pointData = getPointFormulaPointDataDO(localDateTime, pointModelMap, pointFormula, dataType);
                if (Objects.nonNull(pointData)) {
                    //成果值放入分量json
                    pointModelMap.put(pointData.getThingIdentity(), pointData.getThingValue());
                    //成果值加入pointDataList
                    pointDataDOList.add(pointData);
                }
            }
        });
        return pointDataDOList;
    }

    @Override
    public void exportExcel(HttpServletResponse response, PointDataStatisticsVO statisticsVO) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("测点数据.xlsx", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            List<PointDataBO> pointDataBOList = pointStatistics(statisticsVO);
            ClassPathResource resource = null;
            switch (statisticsVO.getTimeType()) {
                case 1:
                    resource = new ClassPathResource("template.excel/pointdata-month.xlsx");
                    break;
                case 2:
                    resource = new ClassPathResource("template.excel/pointdata-year.xlsx");
                    break;
                default:
                    resource = new ClassPathResource("template.excel/pointdata.xlsx");
            }

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(pointDataBOList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    public PointDataModelRespVO getModelValueList(PointDataReqVO pointDataReqVO) {
        PointDataModelRespVO pointDataModelRespVO = new PointDataModelRespVO();
        if (CollectionUtil.isEmpty(pointDataReqVO.getInstrumentModelIdList())) {
            return pointDataModelRespVO;
        }

        //默认查询最新数据当前年份所有数据
        if (Objects.isNull(pointDataReqVO.getPointTime())) {
            PointDataJsonDO lastPointDataJson = pointDataJsonService.getLastPointDataJson(pointDataReqVO.getPointId(), null);
            if (lastPointDataJson != null) {
                LocalDateTime[] newDates = new LocalDateTime[2];
                newDates[0] = lastPointDataJson.getPointTime().withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                newDates[1] = lastPointDataJson.getPointTime();
                pointDataReqVO.setPointTime(newDates);
            }
        }

        //pointDataJson查询数据
        Long startTime = System.currentTimeMillis();
        List<PointDataJsonDO> pointDataJsonDOList = pointDataJsonService.getPointDataJsonList(pointDataReqVO);
        log.info("select pointdatajson end {}", System.currentTimeMillis() - startTime);
        if (CollectionUtils.isEmpty(pointDataJsonDOList)) {
            throw exception(ErrorCodeConstants.POINT_DATA_JSON_NULL);
        }

        //时间转换为string类型
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        Map<String, List<BigDecimal>> modelValueMap = new HashMap<>();
        Map<String, PointDataVO> pointDataVOMap = new HashMap<>();
        List<String> autoPointTimeList = new ArrayList<>();
        List<String> manualPointTimeList = new ArrayList<>();
        //分量的最小值和最大值
        Map<Long, BigDecimal> minMap = new HashMap<>();
        Map<Long, BigDecimal> maxMap = new HashMap<>();

        //人工自动化对比
        if (pointDataReqVO.isComparison()) {
            //补齐横轴最小值
            if (Objects.isNull(pointDataReqVO.getDataType())) {
                PointDataJsonDO firstItem = pointDataJsonDOList.get(0);
                List<PointInstrumentModelJsonVO> firstModelList = JSONObject.parseArray(firstItem.getPointData(), PointInstrumentModelJsonVO.class);
                //过滤没有选择的分量
                firstModelList = firstModelList.stream().filter(modelVO -> pointDataReqVO.getInstrumentModelIdList().contains(modelVO.getInstrumentModelId())).toList();
                if (DataTypeEnum.AUTO_COLLECTION.getType().equals(firstItem.getDataType())) {
                    manualData(formatter, modelValueMap, pointDataVOMap, manualPointTimeList, minMap, maxMap, firstItem, firstModelList, true);
                } else {
                    autoData(pointDataReqVO, formatter, modelValueMap, pointDataVOMap, autoPointTimeList, minMap, maxMap, firstItem, firstModelList, true);
                }
            }

            //补齐横轴最大值
            if (Objects.isNull(pointDataReqVO.getDataType())) {
                PointDataJsonDO lastItem = pointDataJsonDOList.get(pointDataJsonDOList.size() - 1);
                List<PointInstrumentModelJsonVO> LastModelList = JSONObject.parseArray(lastItem.getPointData(), PointInstrumentModelJsonVO.class);
                //过滤没有选择的分量
                LastModelList = LastModelList.stream().filter(modelVO -> pointDataReqVO.getInstrumentModelIdList().contains(modelVO.getInstrumentModelId())).toList();
                if (DataTypeEnum.AUTO_COLLECTION.getType().equals(lastItem.getDataType())) {
                    manualData(formatter, modelValueMap, pointDataVOMap, manualPointTimeList, minMap, maxMap, lastItem, LastModelList, true);
                } else {
                    autoData(pointDataReqVO, formatter, modelValueMap, pointDataVOMap, autoPointTimeList, minMap, maxMap, lastItem, LastModelList, true);
                }
            }
        }
        if (Objects.isNull(pointDataReqVO.getDataType()) && !pointDataReqVO.isComparison()) {
            //合并一条线
            pointDataJsonDOList.forEach(item -> {
                List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(item.getPointData(), PointInstrumentModelJsonVO.class);
                //过滤没有选择的分量
                modelList = modelList.stream().filter(modelVO -> pointDataReqVO.getInstrumentModelIdList().contains(modelVO.getInstrumentModelId())).toList();
                for (int i = 0; i < modelList.size(); i++) {
                    PointInstrumentModelJsonVO model = modelList.get(i);
                    modelValueMap.computeIfAbsent(model.getInstrumentModelId() + "-all", k -> new ArrayList<>()).add(model.getThingValue());
                    pointDataVOMap.putIfAbsent(model.getInstrumentModelId() + "-all", new PointDataVO(model.getThingName(), 0, i));
                    compareValue(minMap, maxMap, model);
                }
                manualPointTimeList.add(formatter.format(item.getPointTime()));
            });
        } else {
            pointDataJsonDOList.forEach(item -> {
                List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(item.getPointData(), PointInstrumentModelJsonVO.class);
                //过滤没有选择的分量
                modelList = modelList.stream().filter(modelVO -> pointDataReqVO.getInstrumentModelIdList().contains(modelVO.getInstrumentModelId())).toList();
                //区分人工还是自动
                if (DataTypeEnum.MANUAL.getType().equals(item.getDataType())) {
                    manualData(formatter, modelValueMap, pointDataVOMap, manualPointTimeList, minMap, maxMap, item, modelList, false);
                } else if (DataTypeEnum.AUTO_COLLECTION.getType().equals(item.getDataType())) {
                    autoData(pointDataReqVO, formatter, modelValueMap, pointDataVOMap, autoPointTimeList, minMap, maxMap, item, modelList, false);
                }
            });
        }
        for (Map.Entry<String, PointDataVO> entry : pointDataVOMap.entrySet()) {
            String key = entry.getKey();
            PointDataVO value = entry.getValue();
            value.setData(modelValueMap.get(key));
            //最大最小值
            Long modelId = Long.valueOf(key.split("-")[0]);
            value.setMinValue(minMap.get(modelId));
            value.setMaxValue(maxMap.get(modelId));
            value.setInstrumentModelId(modelId);
        }

        pointDataModelRespVO.setModelValueMap(pointDataVOMap);

        List<PointTimeVO> pointTime = new ArrayList<>();
        if (pointDataReqVO.getDataType() == null || DataTypeEnum.AUTO_COLLECTION.getType().equals(pointDataReqVO.getDataType())) {
            //选择一种数据类型，只需要一条横轴
            if (DataTypeEnum.AUTO_COLLECTION.getType().equals(pointDataReqVO.getDataType())) {
                pointTime.add(new PointTimeVO(autoPointTimeList, 0));
            } else if (!CollectionUtils.isEmpty(autoPointTimeList)) {
                //人工和自动化都有数据，才需要两条横轴
                pointTime.add(new PointTimeVO(autoPointTimeList, 1));
            }
        }
        if (pointDataReqVO.getDataType() == null || DataTypeEnum.MANUAL.getType().equals(pointDataReqVO.getDataType())) {
            pointTime.add(new PointTimeVO(manualPointTimeList, 0));
        }
        pointDataModelRespVO.setPointTime(pointTime);
        return pointDataModelRespVO;
    }

    @Override
    public List<PointDataProcessLineResVO> listPointDataProcessLine(PointDataProcessLineReqVO pointDataProcessLineReqVO) {
        LocalDateTime minTime = pointDataProcessLineReqVO.getMinTime();
        LocalDateTime maxTime = pointDataProcessLineReqVO.getMaxTime();
        List<Long> pointIdList = pointDataProcessLineReqVO.getPointIdList();

        // 将开始时间和截止时间处理成可以作为查询条件的形式
        List<PointDataJsonPointTimeBO> pointTimeList = new ArrayList<>();
        if (Objects.isNull(minTime) || Objects.isNull(maxTime)) { // 如果用户没选择开始时间和截止时间，那么使用每个测点的最大检测时间所属年份的1月1号0时0分0秒作为开始时间，最大检测时间作为截止时间
            pointTimeList = Optional.ofNullable(pointDataJsonMapper.listMaxPointTimeByPointIdList(pointIdList))
                    .orElse(new ArrayList<>()).stream().map(pointDataJsonDO -> {
                        LocalDateTime tempMaxTime = null;
                        LocalDateTime tempMinTime = null;
                        LocalDateTime pointTime = pointDataJsonDO.getPointTime();
                        tempMaxTime = pointTime;
                        tempMinTime = pointTime.withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                        PointDataJsonPointTimeBO pointDataJsonMaxPointTimeBO = new PointDataJsonPointTimeBO();
                        pointDataJsonMaxPointTimeBO.setPointId(pointDataJsonDO.getPointId());
                        pointDataJsonMaxPointTimeBO.setMaxTime(tempMaxTime);
                        pointDataJsonMaxPointTimeBO.setMinTime(tempMinTime);
                        return pointDataJsonMaxPointTimeBO;
            }).collect(Collectors.toList());
        } else { // 用户选择了开始时间和截止时间
            pointTimeList = Optional.ofNullable(pointIdList).orElse(new ArrayList<>()).stream().map(pointId->{
                PointDataJsonPointTimeBO pointDataJsonPointTimeBO = new PointDataJsonPointTimeBO();
                pointDataJsonPointTimeBO.setPointId(pointId);
                pointDataJsonPointTimeBO.setMaxTime(maxTime);
                pointDataJsonPointTimeBO.setMinTime(minTime);
                return pointDataJsonPointTimeBO;
            }).collect(Collectors.toList());
        }

        // 根据部分查询条件查询出过程线要展示的数据
        List<PointDataJsonDO> pointDataJsonDOList = pointDataJsonMapper.list4ProcessLine(
                pointDataProcessLineReqVO.getProjectId(), pointTimeList,
                pointDataProcessLineReqVO.getDataTypeList(), pointDataProcessLineReqVO.getDataStatusList(),
                pointDataProcessLineReqVO.getReviewStatusList());

        // 查询出的要展示的数据，一条数据包含多个分量的值，为了后续方便写逻辑，现在以分量为单位把一条数据拆成多个
        List<PointDataJsonBO> pointDataJsonBOList = new ArrayList<>();
        Optional.ofNullable(pointDataJsonDOList).orElse(new ArrayList<>()).forEach(pointDataJson->{
            List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(pointDataJson.getPointData(), PointInstrumentModelJsonVO.class);
            modelList.forEach(model->{
                PointDataJsonBO temp = new PointDataJsonBO();
                temp.setId(pointDataJson.getId());
                temp.setProjectId(pointDataJson.getProjectId());
                temp.setPointId(pointDataJson.getPointId());
                temp.setPointTime(pointDataJson.getPointTime());
                temp.setInstrumentId(pointDataJson.getInstrumentId());
                temp.setInstrumentModelId(model.getInstrumentModelId());
                temp.setThingName(model.getThingName());
                temp.setThingIdentity(model.getThingIdentity());
                temp.setThingValue(model.getThingValue());
                temp.setDataType(pointDataJson.getDataType());
                temp.setDataStatus(pointDataJson.getDataStatus());
                temp.setCreator(pointDataJson.getCreator());
                temp.setCreateTime(pointDataJson.getCreateTime());
                temp.setUpdater(pointDataJson.getUpdater());
                temp.setUpdateTime(pointDataJson.getUpdateTime());
                temp.setDeleted(pointDataJson.getDeleted());
                temp.setReviewStatus(pointDataJson.getReviewStatus());
                temp.setReviewer(pointDataJson.getReviewer());
                temp.setReviewName(pointDataJson.getReviewName());
                temp.setReviewOpinion(pointDataJson.getReviewOpinion());
                temp.setRollbackImportId(pointDataJson.getRollbackImportId());
                pointDataJsonBOList.add(temp);
            });
        });


        // 前端传过来的分量id
        Set<Long> instrumentModelIdSet = new HashSet<>(pointDataProcessLineReqVO.getInstrumentModelIdList());
        // 被拆分的数据按分量过滤（过滤完之后的数据就是要在页面上展示的数据）
        List<PointDataJsonBO> showPointDataJsonBOList = pointDataJsonBOList.stream().filter(item->{
            return instrumentModelIdSet.contains(item.getInstrumentModelId());
        }).collect(Collectors.toList());

        // 要展示的数据所属的仪器类型信息
        List<Long> tempInstrumentIdList = new ArrayList<>(showPointDataJsonBOList.stream()
                .map(PointDataJsonBO::getInstrumentId).collect(Collectors.toSet()));
        Map<Long, InstrumentDO> instrumentDOMap = Optional.ofNullable(instrumentService.listByIdList(tempInstrumentIdList)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(InstrumentDO::getId, item->item));
        // 要展示的数据所属的测点信息
        List<Long> tempPointIdList = new ArrayList<>(showPointDataJsonBOList.stream()
                .map(PointDataJsonBO::getPointId).collect(Collectors.toSet()));
        Map<Long, PointDO> pointDOMap = Optional.ofNullable(pointService.listByIdList(tempPointIdList)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(PointDO::getId, item->item));
        // 要展示的数据所属的分量信息
        List<Long> tempInstrumentModelIdList = new ArrayList<>(showPointDataJsonBOList.stream()
                .map(PointDataJsonBO::getInstrumentModelId).collect(Collectors.toSet()));
        Map<Long, InstrumentModelDO> instrumentModelDOMap = Optional.ofNullable(instrumentModelService.listByIdList(
                tempInstrumentModelIdList)).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(InstrumentModelDO::getId, item->item));

        // 将格式转为前端需要的格式
        List<PointDataProcessLineResVO> pointDataProcessLineResVOList = new ArrayList<>();
        showPointDataJsonBOList.stream().collect(Collectors.groupingBy(temp->temp.getInstrumentId()+"-"+temp.getPointId())).forEach((instrumentPoint, instrumentPointList)->{
            String [] instrumentPointArray = instrumentPoint.split("-");
            String instrumentId = instrumentPointArray[0];
            String pointId = instrumentPointArray[1];

            InstrumentDO instrumentDO = Optional.ofNullable(instrumentDOMap.get(instrumentId)).orElse(new InstrumentDO());

            PointDO pointDO = Optional.ofNullable(pointDOMap.get(pointId)).orElse(new PointDO());

            // 标题
            PointDataProcessLineTitleVO title = new PointDataProcessLineTitleVO();
            title.setText(instrumentDO.getInstrumentName()+"过程线");

            // 鼠标提示
            PointDataProcessLineTootipVO tootip = new PointDataProcessLineTootipVO();
            tootip.setTrigger("axis");

            // 图例
            List<Long> tempInstrumentModelIdArray = instrumentPointList.stream().map(PointDataJsonBO::getInstrumentModelId).distinct().collect(Collectors.toList());
            List<String> legendData = tempInstrumentModelIdArray.stream().map(temp->{
                InstrumentModelDO tempInstrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(temp)).orElse(new InstrumentModelDO());
                StringJoiner stringJoiner = new StringJoiner(".").add(pointDO.getPointCode()).add(tempInstrumentModelDO.getThingName());
                return stringJoiner.toString();
            }).collect(Collectors.toList());
            PointDataProcessLineLegendVO legend = new PointDataProcessLineLegendVO();
            legend.setData(legendData);

            // x轴刻度
            PointDataProcessLineAxisLabelVO xAxisLabel = new PointDataProcessLineAxisLabelVO();
            xAxisLabel.setShow(true);
            // x轴数据
            List<LocalDateTime> xAxisdata = instrumentPointList.stream().sorted(Comparator.comparing(PointDataJsonBO::getPointTime)).map(PointDataJsonBO::getPointTime).distinct().collect(Collectors.toList());
            // x轴轴线
            PointDataProcessLineXaxisLineVO xAxisLine = new PointDataProcessLineXaxisLineVO();
            xAxisLine.setShow(true);
            xAxisLine.setOnZero(false);
            // x轴
            PointDataProcessLineXaxisVO xAxis = new PointDataProcessLineXaxisVO();
            xAxis.setType("category");
            xAxis.setBoundaryGap(false);
            xAxis.setAxisLabel(xAxisLabel);
            xAxis.setData(xAxisdata);
            xAxis.setAxisLine(xAxisLine);
            xAxis.setPosition("bottom");

            // y轴集合
            List<PointDataProcessLineYaxisVO> yAxisList = new ArrayList<>();
            AtomicInteger index = new AtomicInteger(0);
            instrumentPointList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId)).forEach((instrumentModelId, modelList)->{
                // y轴名称
                InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(instrumentModelId)).orElse(new InstrumentModelDO());
                StringBuilder name = new StringBuilder().append(instrumentModelDO.getThingName());
                if (StringUtils.isNotBlank(instrumentModelDO.getThingUnit())) {
                    name.append("(").append(instrumentModelDO.getThingUnit()).append(")");
                }

                // 计算当有多个y轴时，这些y轴分布位置
                boolean isRight = index.incrementAndGet()%2==0;

                // y轴轴线
                PointDataProcessLineYaxisLineVO yAxisLine = new PointDataProcessLineYaxisLineVO();
                yAxisLine.setShow(true);

                // y轴刻度
                PointDataProcessLineAxisTickVO yAxisTick = new PointDataProcessLineAxisTickVO();
                yAxisTick.setShow(true);

                // y轴
                PointDataProcessLineYaxisVO yAxis = new PointDataProcessLineYaxisVO();
                yAxis.setType("value");
                yAxis.setName(name.toString());
                if (isRight) {
                    yAxis.setPosition("right");
                } else {
                    yAxis.setPosition("left");
                }
                yAxis.setAlignTicks(true);
                yAxis.setAxisLine(yAxisLine);
                yAxis.setAxisTick(yAxisTick);

                yAxisList.add(yAxis);
            });

            // 数据集合
            List<PointDataProcessLineSeriesVO> seriesList = new ArrayList<>();
            instrumentPointList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId)).forEach((modelId, modelList)->{
                InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(modelId)).orElse(new InstrumentModelDO());

                PointDataProcessLineItemStyleVO itemStyle = new PointDataProcessLineItemStyleVO();
                itemStyle.setColor("#FF3E3E");

                List<BigDecimal> data = modelList.stream().sorted(Comparator.comparing(PointDataJsonBO::getPointTime)).map(PointDataJsonBO::getThingValue).collect(Collectors.toList());

                PointDataProcessLineSeriesVO series = new PointDataProcessLineSeriesVO();
                series.setName(pointDO.getPointCode()+"-"+instrumentModelDO.getThingName());
                series.setType("line");
                series.setSymbolSize(0);
                series.setItemStyle(itemStyle);
                series.setData(data);
                seriesList.add(series);
            });


            PointDataProcessLineResVO pointDataProcessLineResVO = new PointDataProcessLineResVO();
            pointDataProcessLineResVO.setTitle(title);
            pointDataProcessLineResVO.setTootip(tootip);
            pointDataProcessLineResVO.setLegend(legend);
            pointDataProcessLineResVO.setXAxis(xAxis);
            pointDataProcessLineResVO.setYAxis(yAxisList);
            pointDataProcessLineResVO.setSeries(seriesList);
            pointDataProcessLineResVOList.add(pointDataProcessLineResVO);
        });
        return pointDataProcessLineResVOList;
    }

    @Override
    public List<PointDataDistributionChartResVO> listPointDataDistributionChart(PointDataDistributionChartReqVO pointDataDistributionChartReqVO) {

        // 根据部分查询条件查询出过程线要展示的数据
        List<PointDataJsonDO> pointDataJsonDOList = pointDataJsonMapper.list4DistributionChart(
                pointDataDistributionChartReqVO.getProjectId(), pointDataDistributionChartReqVO.getPointIdList(),
                pointDataDistributionChartReqVO.getTimeIntervalUnit(), pointDataDistributionChartReqVO.getPointTimeList(),
                pointDataDistributionChartReqVO.getDataTypeList(), pointDataDistributionChartReqVO.getDataStatusList(),
                pointDataDistributionChartReqVO.getReviewStatusList());

        // 查询出的要展示的数据，一条数据包含多个分量的值，为了后续方便写逻辑，现在以分量为单位把一条数据拆成多个
        List<PointDataJsonBO> pointDataJsonBOList = new ArrayList<>();
        Optional.ofNullable(pointDataJsonDOList).orElse(new ArrayList<>()).forEach(pointDataJson->{
            List<PointInstrumentModelJsonVO> modelList = JSONObject.parseArray(pointDataJson.getPointData(), PointInstrumentModelJsonVO.class);
            modelList.forEach(model->{
                Integer timeIntervalUnit = pointDataDistributionChartReqVO.getTimeIntervalUnit();
                LocalDateTime pointTimeGroup = null;
                if (Objects.equals(timeIntervalUnit, 1)) {
                    pointTimeGroup = pointDataJson.getPointTime().withHour(0).withMinute(0).withSecond(0);
                } else if (Objects.equals(timeIntervalUnit, 2)) {
                    pointTimeGroup = pointDataJson.getPointTime().withMinute(0).withSecond(0);
                }

                PointDataJsonBO temp = new PointDataJsonBO();
                temp.setId(pointDataJson.getId());
                temp.setProjectId(pointDataJson.getProjectId());
                temp.setPointId(pointDataJson.getPointId());
                temp.setPointTime(pointDataJson.getPointTime());
                temp.setPointTimeGroup(pointTimeGroup);
                temp.setInstrumentId(pointDataJson.getInstrumentId());
                temp.setInstrumentModelId(model.getInstrumentModelId());
                temp.setThingName(model.getThingName());
                temp.setThingIdentity(model.getThingIdentity());
                temp.setThingValue(model.getThingValue());
                temp.setDataType(pointDataJson.getDataType());
                temp.setDataStatus(pointDataJson.getDataStatus());
                temp.setCreator(pointDataJson.getCreator());
                temp.setCreateTime(pointDataJson.getCreateTime());
                temp.setUpdater(pointDataJson.getUpdater());
                temp.setUpdateTime(pointDataJson.getUpdateTime());
                temp.setDeleted(pointDataJson.getDeleted());
                temp.setReviewStatus(pointDataJson.getReviewStatus());
                temp.setReviewer(pointDataJson.getReviewer());
                temp.setReviewName(pointDataJson.getReviewName());
                temp.setReviewOpinion(pointDataJson.getReviewOpinion());
                temp.setRollbackImportId(pointDataJson.getRollbackImportId());
                pointDataJsonBOList.add(temp);
            });
        });


        // 前端传过来的分量id
        Set<Long> instrumentModelIdSet = new HashSet<>(pointDataDistributionChartReqVO.getInstrumentModelIdList());
        // 被拆分的数据按分量过滤
        List<PointDataJsonBO> tempPointDataJsonBOList = pointDataJsonBOList.stream().filter(item->{
            return instrumentModelIdSet.contains(item.getInstrumentModelId());
        }).collect(Collectors.toList());

        List<PointDataJsonBO> showPointDataJsonBOList = new ArrayList<>();
        // 一个时间内可能会有多条数据，只要时间最早的那条数据
        tempPointDataJsonBOList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getPointId)).forEach((pointId, pointList)->{
            pointList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId)).forEach((instrumentModeId, modeList)->{
                modeList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getPointTimeGroup)).forEach((pointTimeGroup, groupList)->{
                    groupList.stream().min(Comparator.comparing(PointDataJsonBO::getPointTime)).ifPresent(showPointDataJsonBOList::add);
                });
            });
        });

        // 要展示的数据所属的仪器类型信息
        List<Long> tempInstrumentIdList = new ArrayList<>(showPointDataJsonBOList.stream()
                .map(PointDataJsonBO::getInstrumentId).collect(Collectors.toSet()));
        Map<Long, InstrumentDO> instrumentDOMap = Optional.ofNullable(instrumentService.listByIdList(tempInstrumentIdList)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(InstrumentDO::getId, item->item));
        // 要展示的数据所属的分量信息
        List<Long> tempInstrumentModelIdList = new ArrayList<>(showPointDataJsonBOList.stream()
                .map(PointDataJsonBO::getInstrumentModelId).collect(Collectors.toSet()));
        Map<Long, InstrumentModelDO> instrumentModelDOMap = Optional.ofNullable(instrumentModelService.listByIdList(
                        tempInstrumentModelIdList)).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(InstrumentModelDO::getId, item->item));

        // 将格式转为前端需要的格式
        List<PointDataDistributionChartResVO> pointDataDistributionChartResVOList = new ArrayList<>();
        showPointDataJsonBOList.stream().collect(Collectors.groupingBy(PointDataJsonBO::getInstrumentModelId)).forEach((instrumentModelId, modelList)->{

            InstrumentModelDO instrumentModelDO = Optional.ofNullable(instrumentModelDOMap.get(instrumentModelId)).orElse(new InstrumentModelDO());

            InstrumentDO instrumentDO = Optional.ofNullable(instrumentDOMap.get(instrumentModelDO.getInstrumentId())).orElse(new InstrumentDO());

            // 标题
            PointDataDistributionChartTitleVO title = new PointDataDistributionChartTitleVO();
            title.setText(instrumentDO.getInstrumentName() + instrumentModelDO.getThingName() + "分布图");

            // 鼠标提示
            PointDataDistributionChartTootipVO tootip = new PointDataDistributionChartTootipVO();
            tootip.setTrigger("axis");

            // 图例
            List<String> legenddata = modelList.stream().map(temp->{
                LocalDateTime pointTimeGroup = temp.getPointTimeGroup();
                return pointTimeGroup.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }).collect(Collectors.toList());
            PointDataDistributionChartLegendVO legend = new PointDataDistributionChartLegendVO();
            legend.setData(legenddata);

            // x轴刻度
            PointDataDistributionChartAxisLabelVO xAxisLabel = new PointDataDistributionChartAxisLabelVO();
            xAxisLabel.setShow(true);

            // TODO x轴数据


            // x轴
            PointDataDistributionChartXaxisVO xAxis = new PointDataDistributionChartXaxisVO();
            xAxis.setType("category");
            xAxis.setBoundaryGap(false);
            xAxis.setAxisLabel(xAxisLabel);
            xAxis.setData(null);

            PointDataDistributionChartResVO pointDataDistributionChartResVO = new PointDataDistributionChartResVO();
            pointDataDistributionChartResVO.setTitle(title);
            pointDataDistributionChartResVO.setTootip(tootip);
            pointDataDistributionChartResVO.setLegend(legend);
            pointDataDistributionChartResVO.setXAxis(xAxis);
            pointDataDistributionChartResVOList.add(pointDataDistributionChartResVO);
        });
        return pointDataDistributionChartResVOList;
    }

    private void autoData(PointDataReqVO pointDataReqVO, DateTimeFormatter formatter, Map<String, List<BigDecimal>> modelValueMap,
                          Map<String, PointDataVO> pointDataVOMap, List<String> autoPointTimeList, Map<Long, BigDecimal> minMap, Map<Long, BigDecimal> maxMap,
                          PointDataJsonDO item, List<PointInstrumentModelJsonVO> modelList, boolean isNull) {
        for (int i = 0; i < modelList.size(); i++) {
            PointInstrumentModelJsonVO model = modelList.get(i);
            modelValueMap.computeIfAbsent(model.getInstrumentModelId() + "-auto", k -> new ArrayList<>()).add(isNull ? null : model.getThingValue());
            //选择一种数据类型，只需要一条横轴
            if (DataTypeEnum.AUTO_COLLECTION.getType().equals(pointDataReqVO.getDataType())) {
                pointDataVOMap.putIfAbsent(model.getInstrumentModelId() + "-auto", new PointDataVO(model.getThingName() + "-自动化", 0, i));
            } else {
                pointDataVOMap.putIfAbsent(model.getInstrumentModelId() + "-auto", new PointDataVO(model.getThingName() + "-自动化", 1, i));
            }
            compareValue(minMap, maxMap, model);
        }
        autoPointTimeList.add(formatter.format(item.getPointTime()));
    }

    private void manualData(DateTimeFormatter formatter, Map<String, List<BigDecimal>> modelValueMap, Map<String, PointDataVO> pointDataVOMap,
                            List<String> manualPointTimeList, Map<Long, BigDecimal> minMap, Map<Long, BigDecimal> maxMap,
                            PointDataJsonDO item, List<PointInstrumentModelJsonVO> modelList, boolean isNull) {
        for (int i = 0; i < modelList.size(); i++) {
            PointInstrumentModelJsonVO model = modelList.get(i);
            modelValueMap.computeIfAbsent(model.getInstrumentModelId() + "-manual", k -> new ArrayList<>()).add(isNull ? null : model.getThingValue());
            pointDataVOMap.putIfAbsent(model.getInstrumentModelId() + "-manual", new PointDataVO(model.getThingName() + "-人工录入", 0, i));

            compareValue(minMap, maxMap, model);
        }
        manualPointTimeList.add(formatter.format(item.getPointTime()));
    }

    /**
     * 获取分量的最大最小值
     *
     * @param minMap
     * @param maxMap
     * @param model
     */
    private void compareValue(Map<Long, BigDecimal> minMap, Map<Long, BigDecimal> maxMap, PointInstrumentModelJsonVO model) {
        if (null != model.getThingValue()) {
            if (null == minMap.get(model.getInstrumentModelId()) || minMap.get(model.getInstrumentModelId()).compareTo(model.getThingValue()) > 0) {
                minMap.put(model.getInstrumentModelId(), model.getThingValue());
            }
            if (null == maxMap.get(model.getInstrumentModelId()) || maxMap.get(model.getInstrumentModelId()).compareTo(model.getThingValue()) < 0) {
                maxMap.put(model.getInstrumentModelId(), model.getThingValue());
            }
        }
    }

    /**
     * 根据数据类型，返回适用类型
     * 数据类型=人工，适用类型=人工+全部
     *
     * @param dataType
     * @return
     */
    private List<Integer> getApplyTypeByData(Integer dataType) {
        List<Integer> applyTypeList = new ArrayList<>();
        applyTypeList.add(ApplyTypeEnum.ALL.getCode());
        if (DataTypeEnum.MANUAL.getType().equals(dataType)) {
            applyTypeList.add(ApplyTypeEnum.MANUAL.getCode());
        } else {
            applyTypeList.add(ApplyTypeEnum.AUTO.getCode());
        }
        return applyTypeList;
    }

    /**
     * 计算某个测点公式，返回结果值pointData数据
     *
     * @param localDateTime
     * @param paramsMap
     * @param pointFormulaDO
     * @return
     */
    private PointDataDO getPointFormulaPointDataDO(LocalDateTime localDateTime, Map<String, Object> paramsMap, PointFormulaDO pointFormulaDO, Integer dataType) {
        //计算公式的参数变量:参数+原始分量+中间分量
        Map<String, Object> pointFormulaParams = new HashMap<>(paramsMap);
        Long pointId = pointFormulaDO.getPointId();
        PointDataDO pointData = null;
        InstrumentModelDO instrumentModel = instrumentModelService.getInstrumentModel(pointFormulaDO.getInstrumentModelId());
        if (Objects.nonNull(instrumentModel)) {
            getPointParams(localDateTime, dataType, pointFormulaParams, pointId);
            //公式关联变量列表的真实值
            List<FormulaModelDO> formulaModelDOList = formulaModelService.getFormulaModelByPointFormulaId(pointFormulaDO.getId());
            if (CollectionUtil.isNotEmpty(formulaModelDOList)) {
                formulaModelDOList.forEach(formulaModelDO -> {
                    Map<String, Object> modelValue = getModelValue(pointId, localDateTime, formulaModelDO);
                    pointFormulaParams.putAll(modelValue);
                });
            }
            //公式关联测点的真实值
            List<FormulaPointDO> formulaPointList = formulaPointService.getFormulaPointByPointFormulaId(pointFormulaDO.getId());
            if (CollectionUtil.isNotEmpty(formulaPointList)) {
                formulaPointList.forEach(formulaPointDO -> {
                    //测点
                    PointDO pointDO = pointService.getPoint(formulaPointDO.getPointId());
                    //分量标识符
                    InstrumentModelDO model = instrumentModelService.getInstrumentModel(formulaPointDO.getInstrumentModelId());
                    if (Objects.nonNull(pointDO) && Objects.nonNull(model)) {
                        Map<String, Object> pointParamsMap = getPointValue(localDateTime, formulaPointDO);
                        pointFormulaParams.put(pointDO.getPointCode(), pointParamsMap);
                    }
                });
            }
            //替换公式中的函数
            String expression = pointFormulaDO.getCalcFormula();
            CommonResult<List<DictDataRespDTO>> dictDataList = dictDataApi.getDictDataList(DICT_FORMULA);
            for (DictDataRespDTO item : dictDataList.getData()) {
                String prefix = item.getLabel().split("\\(")[0];
                String fun = MessageFormat.format("math.{0}", prefix);
                expression = expression.replace(prefix, fun);
            }
            //执行引擎解析公式
            Object result = AviatorUtils.execute(expression, pointFormulaParams);
            BigDecimal value = Objects.isNull(result) ? null : AviatorUtils.getBigDecimal(result);
            pointData = createPointData(instrumentModel, pointId, localDateTime, value, dataType);
        } else {
            log.warn("pointId {} bind instrumentModeId {} not get instrumentModel", pointId, pointFormulaDO.getInstrumentModelId());
        }
        return pointData;
    }


    @Override
    public PageResult<PointDataBO> getPointDataBOPage(PointDataPageReqVO pageReqVO) {
        PointInstrumentPageReqVO pageReqVO1 = new PointInstrumentPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        if (Objects.nonNull(pageReqVO.getPointTime())) {
            if (pageReqVO.getTimeType().equals(1)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusMonths(1);
                pageReqVO.setPointTime(newDates);
            } else if (pageReqVO.getTimeType().equals(2)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusYears(1);
                pageReqVO.setPointTime(newDates);
            } else if (pageReqVO.getTimeType().equals(3)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusDays(1);
                pageReqVO.setPointTime(newDates);
            }
        }
        PageResult<PointInstrumentModelBO> pointPageResult = pointService.getPointInstrumentModelBOPage(pageReqVO1);
        PageResult<PointDataBO> pointDataPageResult = new PageResult<>(pointPageResult.getTotal());
        if (!CollectionUtils.isEmpty(pointPageResult.getList())) {
            List<Long> pointIdList = pointPageResult.getList().stream().map(PointInstrumentModelBO::getPointId).distinct().toList();
            List<Long> instrumentModelIds = pointPageResult.getList().stream().map(PointInstrumentModelBO::getInstrumentModelId).distinct().toList();
            Map<String, PointDataBO> pointDataBOMap = getPointDataBOLimitMap(pageReqVO.getProjectId(), pointIdList, instrumentModelIds, pageReqVO);
            pointDataPageResult.setList(pointPageResult.getList().stream().map(item -> {
                PointDataBO pointDataBO = new PointDataBO();
                org.springframework.beans.BeanUtils.copyProperties(item, pointDataBO);
                PointDataBO pointDataExistBO = pointDataBOMap.get(pointDataBO.getPointId() + "~" + pointDataBO.getInstrumentModelId());
                if (Objects.nonNull(pointDataExistBO)) {
                    org.springframework.beans.BeanUtils.copyProperties(pointDataExistBO, pointDataBO,
                            "projectId", "pointId", "pointCode", "instrumentModelId", "thingIdentity", "thingName", "thingUnit");
                }
                return pointDataBO;
            }).toList());
        }
        return pointDataPageResult;
    }

    @Override
    public PageResult<PointDataBO> pointStatisticsPage(PointDataPageReqVO pageReqVO) {
        PointInstrumentPageReqVO pageReqVO1 = new PointInstrumentPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        if (Objects.nonNull(pageReqVO.getPointTime())) {
            if (pageReqVO.getTimeType().equals(1)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusMonths(1);
                pageReqVO.setPointTime(newDates);
            } else if (pageReqVO.getTimeType().equals(2)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusYears(1);
                pageReqVO.setPointTime(newDates);
            } else if (pageReqVO.getTimeType().equals(3)) {
                LocalDate[] newDates = new LocalDate[2];
                newDates[0] = pageReqVO.getPointTime()[0];
                newDates[1] = pageReqVO.getPointTime()[1].plusDays(1);
                pageReqVO.setPointTime(newDates);
            }
        }
        PageResult<PointInstrumentModelBO> pointPageResult = pointService.getPointInstrumentModelBOPage(pageReqVO1);
        PageResult<PointDataBO> pointDataPageResult = new PageResult<>(pointPageResult.getTotal());
        if (CollectionUtils.isEmpty(pointPageResult.getList())) {
            return pointDataPageResult;
        }

        List<Long> pointIdList = pointPageResult.getList().stream().map(PointInstrumentModelBO::getPointId).distinct().toList();
        List<Long> instrumentModelIds = pointPageResult.getList().stream().map(PointInstrumentModelBO::getInstrumentModelId).distinct().toList();
        Map<String, PointDataBO> pointDataBOMap = new HashMap<>();
        Long start = System.currentTimeMillis();
        instrumentModelIds.forEach(o -> pointDataBOMap.putAll(pointDataBOLimitMap1(pointIdList, o, pageReqVO)));
        System.err.println("总共:" + (System.currentTimeMillis() - start));
        pointPageResult.getList().forEach(item -> {
            PointDataBO pointDataBO = new PointDataBO();
            org.springframework.beans.BeanUtils.copyProperties(item, pointDataBO);
            PointDataBO pointDataExistBO = pointDataBOMap.get(pointDataBO.getPointId() + "~" + pointDataBO.getInstrumentModelId());
            if (Objects.nonNull(pointDataExistBO)) {
                org.springframework.beans.BeanUtils.copyProperties(pointDataExistBO, pointDataBO,
                        "projectId", "pointId", "pointCode", "instrumentModelId", "thingIdentity", "thingName", "thingUnit");
            }
            pointDataPageResult.getList().add(pointDataBO);
        });
        return pointDataPageResult;
    }

    @Override
    public List<PointDataBO> pointStatistics(PointDataStatisticsVO statisticsVO) {
        if (Objects.nonNull(statisticsVO.getPointTime())) {
            LocalDateTime[] newDates = new LocalDateTime[2];
            if (statisticsVO.getTimeType().equals(1)) {
                newDates[0] = statisticsVO.getPointTime()[0];
                newDates[1] = statisticsVO.getPointTime()[1];
            } else if (statisticsVO.getTimeType().equals(2)) {
                newDates[0] = statisticsVO.getPointTime()[0];
                newDates[1] = statisticsVO.getPointTime()[1];
            } else if (statisticsVO.getTimeType().equals(3)) {
                newDates[0] = statisticsVO.getPointTime()[0];
                newDates[1] = statisticsVO.getPointTime()[1];
            }
            statisticsVO.setPointTime(newDates);
        }
        //测点-分量数据
        PointInstrumentPageReqVO pageReqVO1 = new PointInstrumentPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(statisticsVO, pageReqVO1);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<PointInstrumentModelBO> pointInstrumentModelBOPage = pointService.getPointInstrumentModelBOPage(pageReqVO1);
        if (CollectionUtil.isEmpty(pointInstrumentModelBOPage.getList())) {
            return null;
        }

        List<PointDataBO> resultList = new ArrayList<>();
        List<Long> pointIdList = statisticsVO.getPointIds();
        List<Long> instrumentModelIds = statisticsVO.getInstrumentModelIds();
        Long start = System.currentTimeMillis();
        if (statisticsVO.getTimeType().equals(3)) {
            Map<String, PointDataBO> pointDataBOMap = new HashMap<>();
            instrumentModelIds.forEach(o -> pointDataBOMap.putAll(pointDataBOLimitMap1(pointIdList, o, BeanUtils.toBean(statisticsVO, PointDataPageReqVO.class))));
            pointInstrumentModelBOPage.getList().forEach(item -> {
                PointDataBO pointDataBO = new PointDataBO();
                org.springframework.beans.BeanUtils.copyProperties(item, pointDataBO);
                PointDataBO pointDataExistBO = pointDataBOMap.get(pointDataBO.getPointId() + "~" + pointDataBO.getInstrumentModelId());
                if (Objects.nonNull(pointDataExistBO)) {
                    org.springframework.beans.BeanUtils.copyProperties(pointDataExistBO, pointDataBO,
                            "projectId", "pointId", "pointCode", "instrumentModelId", "thingIdentity", "thingName", "thingUnit");
                }
                //变幅
                if (Objects.nonNull(pointDataBO.getValueMaxHistory()) && Objects.nonNull(pointDataBO.getValueMinHistory())) {
                    pointDataBO.setValueRange(pointDataBO.getValueMaxHistory().subtract(pointDataBO.getValueMinHistory()));
                }
                resultList.add(pointDataBO);
            });
        } else {
            Map<String, PointInstrumentModelBO> pointInstrumentModelBOHashMap = new HashMap<>();
            pointInstrumentModelBOPage.getList().forEach(item -> {
                pointInstrumentModelBOHashMap.put(item.getPointId() + "-" + item.getInstrumentModelId(), item);
            });
            instrumentModelIds.forEach(modeId -> resultList.addAll(getPointDataList(pointIdList, modeId, statisticsVO)));
            resultList.forEach(item -> {
                //测点code
                PointInstrumentModelBO pointInstrumentModelBO = pointInstrumentModelBOHashMap.get(item.getPointId() + "-" + item.getInstrumentModelId());
                if (null != pointInstrumentModelBO) {
                    item.setPointCode(pointInstrumentModelBO.getPointCode());
                    item.setThingIdentity(pointInstrumentModelBO.getThingIdentity());
                    item.setThingUnit(pointInstrumentModelBO.getThingUnit());
                    item.setThingName(pointInstrumentModelBO.getThingName());
                    item.setProjectId(pointInstrumentModelBO.getProjectId());
                }
            });
        }
        System.err.println("总共:" + (System.currentTimeMillis() - start));
        return resultList;
    }

    private List<PointDataBO> getPointDataList(List<Long> pointIds, Long instrumentModelId, PointDataStatisticsVO statisticsVO) {
        List<PointDataBO> pointDataBOList = new ArrayList<>();
        Map<String, LocalDateTime> maxTimeMap = new HashMap<>();
        Map<String, LocalDateTime> minTimeMap = new HashMap<>();
        boolean useAbsoluteValue = Objects.equals(ValueAbsoluteEnum.YES.getType(), statisticsVO.getUseValueAbsolute());
        //数据类型都勾选，查询所有数据；数据类型都不勾选，dataType=0
        if (CollectionUtil.isEmpty(statisticsVO.getDataTypeList())) {
            statisticsVO.setDataType(0);
        }
        if (CollectionUtil.isNotEmpty(statisticsVO.getDataTypeList()) && statisticsVO.getDataTypeList().size() == 1) {
            statisticsVO.setDataType(statisticsVO.getDataTypeList().get(0));
        }

        pointIds.forEach(pointId -> {
            List<CompletableFuture<Void>> task = new ArrayList<>();
            ///平均、最大、最小、测值次数
            CompletableFuture<Void> statisticsValue = CompletableFuture.runAsync(() -> {
                List<PointDataBO> yearMonthDataList = new ArrayList<>();
                if (statisticsVO.getTimeType().equals(1)) {
                    yearMonthDataList = pointDataMapper.yearAndMonthStatistic(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), useAbsoluteValue, statisticsVO.getDataStatusList());
                } else if (statisticsVO.getTimeType().equals(2)) {
                    yearMonthDataList = pointDataMapper.yearStatistic(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), useAbsoluteValue, statisticsVO.getDataStatusList());
                }
                pointDataBOList.addAll(yearMonthDataList);
            });

            //最大值监测时间
            CompletableFuture<Void> maxTime = CompletableFuture.runAsync(() -> {
                List<Map<String, Object>> maxTimeSyncMap = new ArrayList<>();
                if (statisticsVO.getTimeType().equals(1)) {
                    maxTimeSyncMap = pointDataMapper.selectYearAndMonthValuePointTime(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), 1, useAbsoluteValue, statisticsVO.getDataStatusList());
                } else if (statisticsVO.getTimeType().equals(2)) {
                    maxTimeSyncMap = pointDataMapper.selectYearValuePointTime(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), 1, useAbsoluteValue, statisticsVO.getDataStatusList());
                }
                maxTimeSyncMap.forEach(item -> {
                    maxTimeMap.put(item.get("pointId") + "-" + item.get("yearMonth").toString(), (LocalDateTime) item.get("valuePointTime"));
                });
            });

            //最小值监测时间
            CompletableFuture<Void> minTime = CompletableFuture.runAsync(() -> {
                List<Map<String, Object>> minTimeSyncMap = new ArrayList<>();
                if (statisticsVO.getTimeType().equals(1)) {
                    minTimeSyncMap = pointDataMapper.selectYearAndMonthValuePointTime(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), 0, useAbsoluteValue, statisticsVO.getDataStatusList());
                } else if (statisticsVO.getTimeType().equals(2)) {
                    minTimeSyncMap = pointDataMapper.selectYearValuePointTime(pointId, instrumentModelId, statisticsVO.getPointTime()[0], statisticsVO.getPointTime()[1], statisticsVO.getDataTypeList(), 0, useAbsoluteValue, statisticsVO.getDataStatusList());
                }
                minTimeSyncMap.forEach(item -> {
                    minTimeMap.put(item.get("pointId") + "-" + item.get("yearMonth").toString(), (LocalDateTime) item.get("valuePointTime"));
                });
            });

            //CompletableFuture.allOf(min, max, first).join();
            task.add(statisticsValue);
            task.add(maxTime);
            task.add(minTime);
            CompletableFuture.allOf(task.toArray(new CompletableFuture[task.size()])).join();
        });
        if (CollectionUtil.isNotEmpty(pointDataBOList)) {
            pointDataBOList.forEach(pointDataBO -> {
                pointDataBO.setValueMaxPointTime(maxTimeMap.get(pointDataBO.getPointId() + "-" + pointDataBO.getYearAndMonth()));
                pointDataBO.setValueMinPointTime(minTimeMap.get(pointDataBO.getPointId() + "-" + pointDataBO.getYearAndMonth()));
                //变幅
                if (Objects.nonNull(pointDataBO.getValueMax()) && Objects.nonNull(pointDataBO.getValueMin())) {
                    pointDataBO.setValueRange(pointDataBO.getValueMax().subtract(pointDataBO.getValueMin()));
                }
                pointDataBO.setInstrumentModelId(instrumentModelId);
            });
        }
        return pointDataBOList;
    }

    /**
     * 查询测值统计
     *
     * @param pointIds
     * @param instrumentModelId 传递单个更快，用多线程查询汇总
     * @return
     */
    private Map<String, PointDataBO> pointDataBOLimitMap1(List<Long> pointIds, Long instrumentModelId, PointDataPageReqVO pageReqVO) {
        Map<String, PointDataBO> pointDataBOMap = new ConcurrentHashMap<>();
        pointIds.forEach(pointId -> {
            List<CompletableFuture<Void>> task = new ArrayList<>();
            PointDataBO pointDataBO = new PointDataBO();
            pointDataBO.setPointId(pointId);
            pointDataBO.setInstrumentModelId(instrumentModelId);
            //末值
            CompletableFuture<Void> last = CompletableFuture.runAsync(() -> {
                processPointDataBoAttribute(pointDataBO, pageReqVO, Arrays.asList("thing_value", "point_time"), "point_time", false);
            });
            ///平均、最大、最小
            CompletableFuture<Void> composite = CompletableFuture.runAsync(() -> {
                processPointDataCompositeAttribute(pointDataBO, pageReqVO);
            });

            //最小值
            CompletableFuture<Void> min = CompletableFuture.runAsync(() -> {
//                processPointDataBoAttribute(pointDataBO, pageReqVO, Collections.singletonList("thing_value"), "thing_value", false);
                processPointDataBoAttribute(pointDataBO, pageReqVO, Arrays.asList("thing_value", "absolute_value"), "thing_value", false);
            });
            //最大值
            CompletableFuture<Void> max = CompletableFuture.runAsync(() -> {
//                processPointDataBoAttribute(pointDataBO, pageReqVO, Collections.singletonList("thing_value"), "thing_value", true);
                processPointDataBoAttribute(pointDataBO, pageReqVO, Arrays.asList("thing_value", "absolute_value"), "thing_value", true);
            });
            //首值
            CompletableFuture<Void> first = CompletableFuture.runAsync(() -> {
                processPointDataBoAttribute(pointDataBO, pageReqVO, Arrays.asList("thing_value", "absolute_value", "point_time"), null, true);
            });

            //CompletableFuture.allOf(min, max, first).join();
            task.add(min);
            task.add(max);
            task.add(first);
            task.add(composite);
            task.add(last);
            pointDataBOMap.put(pointDataBO.getPointId() + "~" + pointDataBO.getInstrumentModelId(), pointDataBO);
            CompletableFuture.allOf(task.toArray(new CompletableFuture[task.size()])).join();
        });
        return pointDataBOMap;
    }

    /**
     * 设置聚合参数
     * 平均值，最大值，最小值
     *
     * @param pointDataBO
     * @param pageReqVO
     */
    private void processPointDataCompositeAttribute(PointDataBO pointDataBO, PointDataPageReqVO pageReqVO) {
        QueryWrapper<PointDataDO> wrapper = new QueryWrapper<>();
        wrapper.select("MAX(thing_value) as valueMax, AVG(thing_value)  as valueAverage, MIN(thing_value) as valueMin");
        wrapper.eq("point_id", pointDataBO.getPointId())
                .eq("instrument_model_id", pointDataBO.getInstrumentModelId())
                .between("point_time", pageReqVO.getPointTime()[0], pageReqVO.getPointTime()[1]);
        if (!CollectionUtils.isEmpty(pageReqVO.getDataTypeList())) {
            wrapper.in("data_type", pageReqVO.getDataTypeList());
        }
        if (!CollectionUtils.isEmpty(pageReqVO.getDataStatusList())) {
            wrapper.in("data_status", pageReqVO.getDataStatusList());
        }
        long start = System.currentTimeMillis();
        List<Map<String, Object>> pointDataLimitList = pointDataMapper.selectMaps(wrapper);
        System.err.println("平均值:" + (System.currentTimeMillis() - start));
        //数据库数据可能为空，移除空元素
        pointDataLimitList.removeAll(Collections.singleton(null));
        if (CollectionUtils.isEmpty(pointDataLimitList)) {
            return;
        }

        pointDataBO.setValueMaxHistory(BigDecimalUtils.calculateValue(((BigDecimal) pointDataLimitList.get(0).get("valueMax")), 2));
        pointDataBO.setValueMinHistory(BigDecimalUtils.calculateValue(((BigDecimal) pointDataLimitList.get(0).get("valueMin")), 2));
        pointDataBO.setValueAverage(BigDecimalUtils.calculateValue(((BigDecimal) pointDataLimitList.get(0).get("valueAverage")), 2));
    }

    /**
     * 查询测值统计
     *
     * @param projectId
     * @param pointIds
     * @param instrumentModelIds
     * @return
     */
    private Map<String, PointDataBO> getPointDataBOLimitMap(Long projectId, List<Long> pointIds, List<Long> instrumentModelIds, PointDataPageReqVO pageReqVO) {
        Map<String, PointDataBO> pointDataBOMap = new HashMap<>();
        boolean useAbsoluteValue = Objects.equals(ValueAbsoluteEnum.YES.getType(), pageReqVO.getUseValueAbsolute());

        //查询测点分量的历史最大、历史最小
        QueryWrapper<PointDataDO> wrapperHistory = new QueryWrapper<>();
        wrapperHistory.groupBy("point_id", "instrument_model_id ");
        wrapperHistory.select("point_id as pointId", "instrument_model_id as instrumentModelId",
                useAbsoluteValue ? "min(absolute_value)as valueMinHistory" : "min(thing_value)as valueMinHistory",
                useAbsoluteValue ? "max(absolute_value)as valueMaxHistory" : "max(thing_value)as valueMaxHistory");
        wrapperHistory.eq("project_id", projectId)
                .in("point_id", pointIds)
                .in("instrument_model_id", instrumentModelIds);
        if (Objects.nonNull(pageReqVO.getDataType())) {
            wrapperHistory.eq("data_type", pageReqVO.getDataType());
        }

        List<Map<String, Object>> pointDataHistoryList = pointDataMapper.selectMaps(wrapperHistory);
        if (CollectionUtils.isEmpty(pointDataHistoryList)) {
            return pointDataBOMap;
        }

        pointDataHistoryList.forEach(item -> {
            PointDataBO pointDataBO = new PointDataBO();
            pointDataBO.setPointId((Long) item.get("pointId"));
            pointDataBO.setInstrumentModelId((Long) item.get("instrumentModelId"));
            pointDataBO.setValueMaxHistory((BigDecimal) item.get("valueMaxHistory"));
            pointDataBO.setValueMinHistory((BigDecimal) item.get("valueMinHistory"));
            List<CompletableFuture<Void>> result = new ArrayList<>();
            //设置最小值和最小值时间
            result.add(CompletableFuture.runAsync(() -> {
                setPointDataBoAttribute(pointDataBO, pageReqVO, useAbsoluteValue ? "absolute_value" : "thing_value", true, useAbsoluteValue);
            }, damThreadPoolTaskExecutor));
            //设置最大值和最大值时间
            result.add(CompletableFuture.runAsync(() -> {
                setPointDataBoAttribute(pointDataBO, pageReqVO, useAbsoluteValue ? "absolute_value" : "thing_value", false, useAbsoluteValue);
            }, damThreadPoolTaskExecutor));
            //设置首值和首值时间
            result.add(CompletableFuture.runAsync(() -> {
                setPointDataBoAttribute(pointDataBO, pageReqVO, "point_time", true, useAbsoluteValue);
            }, damThreadPoolTaskExecutor));
            //设置尾值和尾值时间
            result.add(CompletableFuture.runAsync(() -> {
                setPointDataBoAttribute(pointDataBO, pageReqVO, "point_time", false, useAbsoluteValue);
            }, damThreadPoolTaskExecutor));
            //设置平均值
            result.add(CompletableFuture.runAsync(() -> {
                setPointDataBoAttributeAverage(pointDataBO, pageReqVO, useAbsoluteValue);
            }, damThreadPoolTaskExecutor));
            CompletableFuture.allOf(result.toArray(new CompletableFuture[0])).join();
            //设置变幅
            if (Objects.nonNull(pointDataBO.getValueMin())) {
                pointDataBO.setValueRange(BigDecimalUtils.calculateValueSub(pointDataBO.getValueMax(), pointDataBO.getValueMin(), 2));
            }
            pointDataBOMap.put(pointDataBO.getPointId() + "~" + pointDataBO.getInstrumentModelId(), pointDataBO);
        });
        return pointDataBOMap;
    }

    /**
     * 查找平均值
     *
     * @param pointDataBO
     * @param pageReqVO
     * @param useAbsoluteValue
     */
    private void setPointDataBoAttributeAverage(PointDataBO pointDataBO, PointDataPageReqVO pageReqVO, boolean useAbsoluteValue) {
        QueryWrapper<PointDataDO> wrapper = new QueryWrapper<>();
        wrapper.select(useAbsoluteValue ? "avg(absolute_value)as valueAverage" : "avg(thing_value)as valueAverage");
        wrapper.eq("project_id", pageReqVO.getProjectId())
                .eq("point_id", pointDataBO.getPointId())
                .eq("instrument_model_id", pointDataBO.getInstrumentModelId())
                .between("point_time", pageReqVO.getPointTime()[0], pageReqVO.getPointTime()[1]);
        if (Objects.nonNull(pageReqVO.getDataType())) {
            wrapper.eq("data_type", pageReqVO.getDataType());
        }
        List<Map<String, Object>> pointDataLimitList = pointDataMapper.selectMaps(wrapper);
        //数据库数据可能为空，移除空元素
        pointDataLimitList.removeAll(Collections.singleton(null));
        if (!CollectionUtils.isEmpty(pointDataLimitList)) {
            pointDataBO.setValueAverage(BigDecimalUtils.calculateValue(((BigDecimal) pointDataLimitList.get(0).get("valueAverage")), 2));
        }
    }

    /**
     * 设置属性值
     *
     * @param pointDataBO
     * @param pageReqVO
     * @param columns
     * @param orderAsc
     */
    private PointDataBO processPointDataBoAttribute(PointDataBO pointDataBO, PointDataPageReqVO pageReqVO, List<String> columns, String orderColum, boolean orderAsc) {
        Map<String, Object> firstDataTimeMap = columFirstDataTime(pointDataBO.getPointId(), pointDataBO.getInstrumentModelId(), pageReqVO, columns, orderColum, orderAsc);
        if (MapUtil.isEmpty(firstDataTimeMap)) {
            return pointDataBO;
        }

        LocalDateTime pointTime = (LocalDateTime) firstDataTimeMap.get("point_time");
        BigDecimal value = (BigDecimal) firstDataTimeMap.get("thing_value");
        BigDecimal absoluteValue = (BigDecimal) firstDataTimeMap.get("absolute_value");
        if (columns.contains("point_time") && !orderAsc) {
            pointDataBO.setValueLast(pageReqVO.getUseValueAbsolute() == 0 ? value : absoluteValue);
            pointDataBO.setValueLastPointTime(pointTime);
        } else if (columns.contains("point_time") && orderAsc) {
            pointDataBO.setValueFirst(pageReqVO.getUseValueAbsolute() == 0 ? value : absoluteValue);
            pointDataBO.setValueFirstPointTime(pointTime);
        } else if (columns.contains("thing_value") && orderAsc) {
            pointDataBO.setValueMin(pageReqVO.getUseValueAbsolute() == 0 ? value : absoluteValue);
            pointDataBO.setValueMinPointTime(pointTime);
        } else if (columns.contains("thing_value")) {
            pointDataBO.setValueMax(pageReqVO.getUseValueAbsolute() == 0 ? value : absoluteValue);
            pointDataBO.setValueMaxPointTime(pointTime);
        }

        return pointDataBO;
    }

    /**
     * 获取指定类型数据id
     *
     * @param pageReqVO
     * @param columns
     * @param orderAsc
     * @return
     */
    private Map<String, Object> columFirstDataTime(Long pointId, Long instrumentId, PointDataPageReqVO pageReqVO, List<String> columns, String orderColum, boolean orderAsc) {
        QueryWrapper<PointDataDO> wrapper = new QueryWrapper<>();
        Set<String> columnSelect = new HashSet<>(columns);
        columnSelect.add("point_time");
        wrapper.select(columnSelect.toArray(new String[columnSelect.size()]))
                .eq("point_id", pointId)
                .eq("instrument_model_id", instrumentId)
                .between("point_time", pageReqVO.getPointTime()[0], pageReqVO.getPointTime()[1]);
        if (!CollectionUtils.isEmpty(pageReqVO.getDataTypeList())) {
            wrapper.in("data_type", pageReqVO.getDataTypeList());
        }
        if (!CollectionUtils.isEmpty(pageReqVO.getDataStatusList())) {
            wrapper.in("data_status", pageReqVO.getDataStatusList());
        }

        //末值
        if (Objects.nonNull(orderColum) && orderColum.equals("point_time") && orderAsc) {
            wrapper.orderByAsc("point_time");
        } else if (Objects.nonNull(orderColum) && orderColum.equals("point_time") && !orderAsc) {
            wrapper.orderByDesc("point_time");
        } else if (Objects.nonNull(orderColum) && orderAsc) {
            wrapper.orderByAsc(orderColum).orderByAsc("id");
        } else if (Objects.nonNull(orderColum)) {
            wrapper.orderByDesc(orderColum).orderByDesc("id");
        }

        wrapper.last("limit 1");
        long start = System.currentTimeMillis();
        List<Map<String, Object>> pointDataLimitList = pointDataMapper.selectMaps(wrapper);
        System.err.println("columns:" + columns + " pointId:" + pointId + " instrumentId:" + instrumentId + ":" + " orderAsc:" + orderAsc + (System.currentTimeMillis() - start));
        return CollUtil.isNotEmpty(pointDataLimitList) ? pointDataLimitList.get(0) : null;
    }


    /**
     * 设置属性值
     *
     * @param pointDataBO
     * @param pageReqVO
     * @param column
     * @param orderAsc
     * @param useAbsoluteValue
     */
    private void setPointDataBoAttribute(PointDataBO pointDataBO, PointDataPageReqVO pageReqVO, String column, boolean orderAsc, boolean useAbsoluteValue) {
        Long valueId = getPointDataSelectId(pointDataBO.getPointId(), pointDataBO.getInstrumentModelId(), pageReqVO, column, orderAsc);
        if (Objects.nonNull(valueId)) {
            PointDataDO pointDataDO = getPointData(valueId);
            if (Objects.nonNull(pointDataDO)) {
                if ((column.equals("thing_value") || column.equals("absolute_value")) && orderAsc) {
                    pointDataBO.setValueMin(!useAbsoluteValue ? pointDataDO.getThingValue() : pointDataDO.getAbsoluteValue());
                    pointDataBO.setValueMinPointTime(pointDataDO.getPointTime());
                } else if (column.equals("thing_value") || column.equals("absolute_value")) {
                    pointDataBO.setValueMax(!useAbsoluteValue ? pointDataDO.getThingValue() : pointDataDO.getAbsoluteValue());
                    pointDataBO.setValueMaxPointTime(pointDataDO.getPointTime());
                } else if (column.equals("point_time") && orderAsc) {
                    pointDataBO.setValueFirst(useAbsoluteValue ? pointDataDO.getAbsoluteValue() : pointDataDO.getThingValue());
                    pointDataBO.setValueFirstPointTime(pointDataDO.getPointTime());
                } else if (column.equals("point_time")) {
                    pointDataBO.setValueLast(useAbsoluteValue ? pointDataDO.getAbsoluteValue() : pointDataDO.getThingValue());
                    pointDataBO.setValueLastPointTime(pointDataDO.getPointTime());
                }
            }
        }
    }


    /**
     * 获取指定类型数据id
     *
     * @param pageReqVO
     * @param column
     * @param orderAsc
     * @return
     */
    private Long getPointDataSelectId(Long pointId, Long instrumentId, PointDataPageReqVO pageReqVO, String column, boolean orderAsc) {
        QueryWrapper<PointDataDO> wrapper = new QueryWrapper<>();
        wrapper.select("id");
        wrapper.eq("project_id", pageReqVO.getProjectId())
                .eq("point_id", pointId)
                .eq("instrument_model_id", instrumentId)
                .between("point_time", pageReqVO.getPointTime()[0], pageReqVO.getPointTime()[1]);
        if (Objects.nonNull(pageReqVO.getDataType())) {
            wrapper.eq("data_type", pageReqVO.getDataType());
        }
        if (orderAsc) {
            wrapper.orderByAsc(column).orderByAsc("id");
        } else {
            wrapper.orderByDesc(column).orderByDesc("id");
        }
        wrapper.last("limit 1");
        List<Map<String, Object>> pointDataLimitList = pointDataMapper.selectMaps(wrapper);
        if (!CollectionUtils.isEmpty(pointDataLimitList)) {
            return (Long) pointDataLimitList.get(0).get("id");
        }
        return null;
    }

    /**
     * 根据变量取值条件，获取变量真实值
     *
     * @param pointId
     * @param localDateTime
     * @param formulaModelDO
     */
    private Map<String, Object> getModelValue(Long pointId, LocalDateTime localDateTime, FormulaModelDO formulaModelDO) {
        Map<String, Object> paramsMap = new HashMap<>();
        //根据取值条件，获取变量真实值
        DataConditionEnum conditionEnum = DataConditionEnum.getByType(formulaModelDO.getDataCondition());
        PointDataDO pointData = null;
        switch (conditionEnum) {
            case RELATIVE_DATA:
                //之前：第几条测值
                if (RELATIVE_DATA_BEFORE.equals(formulaModelDO.getDataValue())) {
                    pointData = getPointData(pointId, formulaModelDO.getInstrumentModelId(), localDateTime, formulaModelDO.getDataUnit());
                }
                break;
            case FIRST_DATA:
                //首次自动化采集的值
                pointData = getFirstPointData(pointId, formulaModelDO.getInstrumentModelId());
                break;
            case TIME_FRAME_DATA:
                LocalDateTime specifyTime = formulaModelDO.getSpecifyTime();
                List<PointDataDO> pointDataDOList = getPointDataByDataUnit(specifyTime, formulaModelDO.getDataUnit(), formulaModelDO.getDataValue(), pointId, formulaModelDO.getInstrumentModelId());
                if (CollectionUtil.isNotEmpty(pointDataDOList)) {
                    pointData = findClosestTime(pointDataDOList, localDateTime);
                }
                break;
            default:
                break;
        }
        if (Objects.nonNull(pointData)) {
            paramsMap.put(pointData.getThingIdentity(), pointData.getThingValue());
        }
        return paramsMap;
    }

    /**
     * 获取关联测点的参数集合
     *
     * @param currentTime
     * @param formulaPointDO
     * @return
     */
    private Map<String, Object> getPointValue(LocalDateTime currentTime, FormulaPointDO formulaPointDO) {
        Map<String, Object> paramsMap = new HashMap<>();
        PointDataDO pointData = null;
        //根据取值条件，获取变量真实值
        PointConditionEnum conditionEnum = PointConditionEnum.getByType(formulaPointDO.getDataCondition());
        switch (conditionEnum) {
            case TIME_FRAME_DATA:
                List<PointDataDO> pointDataDOList = getPointDataByDataUnit(currentTime, formulaPointDO.getDataUnit(), formulaPointDO.getDataValue(), formulaPointDO.getPointId(), formulaPointDO.getInstrumentModelId());
                if (CollectionUtil.isNotEmpty(pointDataDOList)) {
                    pointData = findClosestTime(pointDataDOList, currentTime);
                }
                if (Objects.nonNull(pointData)) {
                    paramsMap.put(pointData.getThingIdentity(), pointData.getThingValue());
                }
                break;
            case CURRENT_DATA:
                //截取到分钟，取当前分钟的第一条数据
                LocalDateTime startTime = currentTime.truncatedTo(ChronoUnit.MINUTES);
                LocalDateTime endTime = startTime.plusMinutes(1);
                List<PointDataDO> pointDataDOS = getPointDataByTime(formulaPointDO.getPointId(), formulaPointDO.getInstrumentModelId(), startTime, endTime);
                if (CollectionUtil.isNotEmpty(pointDataDOS)) {
                    pointData = pointDataDOS.get(0);
                    paramsMap.put(pointData.getThingIdentity(), pointData.getThingValue());
                }

                break;
            case BEFORE_RECENT_DATA:
                pointData = pointDataMapper.selectOne(new LambdaQueryWrapperX<PointDataDO>()
                        .eq(PointDataDO::getPointId, formulaPointDO.getPointId()).eq(PointDataDO::getInstrumentModelId, formulaPointDO.getInstrumentModelId()).orderByDesc(PointDataDO::getPointTime)
                        .last("limit 1"));
                if (Objects.nonNull(pointData)) {
                    paramsMap.put(pointData.getThingIdentity(), pointData.getThingValue());
                }
                break;
            case CUMULATIVE_DATA:
                List<PointDataDO> pointList = getPointDataByDataUnit(currentTime, formulaPointDO.getDataUnit(), formulaPointDO.getDataValue(), formulaPointDO.getPointId(), formulaPointDO.getInstrumentModelId());
                BigDecimal sumValue = new BigDecimal(0);
                if (CollectionUtil.isNotEmpty(pointList)) {
                    for (PointDataDO pointDataDO : pointList) {
                        sumValue = sumValue.add(pointDataDO.getThingValue());
                    }
                    paramsMap.put(pointList.get(0).getThingIdentity(), sumValue);
                }
                break;
            default:
                break;
        }
        return paramsMap;
    }

    /**
     * 根据单位不同，查询时间段内的测点数据
     *
     * @param currentTime
     * @param dataUnit
     * @param dataValue
     * @param pointId
     * @param instrumentModelId
     * @return
     */
    private List<PointDataDO> getPointDataByDataUnit(LocalDateTime currentTime, Integer dataUnit, Integer dataValue, Long pointId, Long instrumentModelId) {
        DataUnitEnum dataUnitEnum = DataUnitEnum.getByType(dataUnit);
        LocalDateTime startTime = currentTime;
        LocalDateTime endTime = currentTime;
        if(dataValue!=null) {
            switch (dataUnitEnum) {
                case MINUTE:
                    startTime = currentTime.minusMinutes(dataValue);
                    endTime = currentTime.plusMinutes(dataValue);
                    break;
                case HOUR:
                    startTime = currentTime.minusHours(dataValue);
                    endTime = currentTime.plusHours(dataValue);
                    break;
                case DAY:
                    startTime = currentTime.minusDays(dataValue);
                    endTime = currentTime.plusDays(dataValue);
                    break;
                default:
                    break;
            }
        }
        return getPointDataByTime(pointId, instrumentModelId, startTime, endTime);
    }

    /**
     * 获取测点参数的真实值
     *
     * @param localDateTime
     * @param dataType
     * @param pointFormulaParams
     * @param pointId
     */
    private void getPointParams(LocalDateTime localDateTime, Integer dataType, Map<String, Object> pointFormulaParams, Long pointId) {
        List<Integer> applyTypeList = getApplyTypeByData(dataType);
        //生效时间内的测点参数，适用类型是全部和自动化的
        List<PointParamDO> pointParamDOList = pointParamService.getPointParamByPointId(pointId)
                .stream().filter(pointParamDO -> applyTypeList.contains(pointParamDO.getApplyType())).toList();
        PointParamDO pointParamDO = null;
        //生效时间为空，所有时间都生效
        List<PointParamDO> nullPointParamDOList = pointParamDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
        if (CollectionUtil.isEmpty(nullPointParamDOList)) {
            //判断是否在生效时间内
            pointParamDOList = pointParamDOList.stream().filter(pointFormulaDO -> pointFormulaDO.getEffectiveStartTime().isBefore(localDateTime) && pointFormulaDO.getEffectiveEndTime().isAfter(localDateTime))
                    .toList();
            if (CollectionUtil.isNotEmpty(pointParamDOList)) {
                pointParamDO = pointParamDOList.get(0);
            }
        } else {
            pointParamDO = nullPointParamDOList.get(0);
        }
        if (Objects.nonNull(pointParamDO)) {
            String calcParam = pointParamDO.getCalcParam();
            List<Map> list = JSON.parseArray(calcParam, Map.class);
            Map<String, Object> map = new HashMap<>();
            list.forEach(item -> {
                item.forEach((key, value) -> {
                    String mapKey = (String) key;
                    if (value instanceof Number) {
                        map.put(mapKey, new BigDecimal(value.toString()));
                    } else if (value instanceof String) {
                        try {
                            map.put(mapKey, new BigDecimal((String) value));
                        } catch (NumberFormatException e) {
                            // 处理无法转换为 BigDecimal 的情况
                            map.put(mapKey, value);
                        }
                    } else {
                        // 处理其他类型的 value
                        map.put(mapKey, value);
                    }
                });
            });
            pointFormulaParams.putAll(map);
        }
    }


    /**
     * 组织pointData
     *
     * @param instrumentModelDO
     * @param pointId
     * @param localDateTime
     * @param value
     * @return
     */
    public PointDataDO createPointData(InstrumentModelDO instrumentModelDO, Long pointId, LocalDateTime localDateTime, BigDecimal value, Integer dataType) {
        PointDataDO pointDataDO = new PointDataDO();
        pointDataDO.setProjectId(instrumentModelDO.getProjectId());
        pointDataDO.setPointId(pointId);
        pointDataDO.setPointTime(localDateTime);
        pointDataDO.setInstrumentId(instrumentModelDO.getInstrumentId());
        pointDataDO.setInstrumentModelId(instrumentModelDO.getId());
        pointDataDO.setThingIdentity(instrumentModelDO.getThingIdentity());
        pointDataDO.setThingName(instrumentModelDO.getThingName());
        //采集类型
        pointDataDO.setDataType(dataType);
        pointDataDO.setDataStatus(DataStatusEnum.UNDETERMINED.getType());
        // pointDataDO.setThingValueOrigin(value);
        if (Objects.nonNull(value)) {
            //按照设置的小数位截取
            Integer decimalLimit = Objects.isNull(instrumentModelDO.getDecimalLimit()) ? 0 : instrumentModelDO.getDecimalLimit();
            BigDecimal resultValue = value.setScale(decimalLimit, RoundingMode.DOWN);
            pointDataDO.setThingValue(resultValue);
            pointDataDO.setAbsoluteValue(resultValue.abs());
            //判断适用类型
            List<Integer> applyTypeList = getApplyTypeByData(dataType);
            List<PointEvaluateDO> pointEvaluateDOList = pointEvaluateService.getPointEvaluateByPointIdAndModelId(pointId, instrumentModelDO.getId())
                    .stream().filter(pointEvaluate -> applyTypeList.contains(pointEvaluate.getApplyType())).toList();
            PointEvaluateDO pointEvaluateDO = null;
            //生效时间为空，所有时间都生效
            List<PointEvaluateDO> nullPointParamDOList = pointEvaluateDOList.stream().filter(item -> item.getEffectiveStartTime() == null || item.getEffectiveEndTime() == null).toList();
            if (CollectionUtil.isEmpty(nullPointParamDOList)) {
                //判断是否在生效时间内
                pointEvaluateDOList = pointEvaluateDOList.stream().filter(pointFormulaDO -> pointFormulaDO.getEffectiveStartTime().isBefore(localDateTime) && pointFormulaDO.getEffectiveEndTime().isAfter(localDateTime))
                        .toList();
                if (CollectionUtil.isNotEmpty(pointEvaluateDOList)) {
                    pointEvaluateDO = pointEvaluateDOList.get(0);
                }
            } else {
                pointEvaluateDO = nullPointParamDOList.get(0);
            }
            if (Objects.nonNull(pointEvaluateDO)) {
                createAlarm(localDateTime, value, pointDataDO, pointEvaluateDO);
                //有评价指标，数据状态判定正常
                pointDataDO.setDataStatus(DataStatusEnum.NORMAL.getType());
                //数据状态根据评价指标判定，且评价指标只有一个
                PointEvaluateDO checkDataStatus = pointEvaluateDO;
                if (value.compareTo(new BigDecimal(checkDataStatus.getWaringUp())) > 0 || value.compareTo(new BigDecimal(checkDataStatus.getWaringDown())) < 0) {
                    pointDataDO.setDataStatus(DataStatusEnum.ANOMALOUS.getType());
                }
                if (value.compareTo(new BigDecimal(checkDataStatus.getAbnormalUp())) > 0 || value.compareTo(new BigDecimal(checkDataStatus.getAbnormalDown())) < 0) {
                    pointDataDO.setDataStatus(DataStatusEnum.ERROR_DATA.getType());
                }
            }
        } else {
            pointDataDO.setDataStatus(DataStatusEnum.ANOMALOUS.getType());
        }
        return pointDataDO;
    }

    /**
     * 根据测点评价指标，产生告警
     *
     * @param localDateTime
     * @param value
     * @param pointDataDO
     * @param pointEvaluateDO
     */
    private void createAlarm(LocalDateTime localDateTime, BigDecimal value, PointDataDO pointDataDO, PointEvaluateDO pointEvaluateDO) {
        // 比较测点评价指标,产生告警
        if (value.compareTo(new BigDecimal(pointEvaluateDO.getAbnormalUp())) > 0 || value.compareTo(new BigDecimal(pointEvaluateDO.getAbnormalDown())) < 0) {
            PointAlarmSaveReqVO pointAlarmSaveReqVO = createPointAlarm(localDateTime, pointDataDO);
            String content = value.compareTo(new BigDecimal(pointEvaluateDO.getAbnormalUp())) > 0 ? value + " > 错误报警上限值 " + pointEvaluateDO.getAbnormalUp() : value + " < 错误报警下限值 " + pointEvaluateDO.getAbnormalDown();
            pointAlarmSaveReqVO.setAlarmType(AlarmTypeEnum.ERROR_ALARM.getType());
            pointAlarmSaveReqVO.setAlarmContent(content);
            log.info("pointId {} create  error alarm {}", pointDataDO.getPointId(), content);
            pointAlarmService.createPointAlarm(pointAlarmSaveReqVO);
            return;
        }
        // 比较测点评价指标,产生告警
        if (value.compareTo(new BigDecimal(pointEvaluateDO.getWaringUp())) > 0 || value.compareTo(new BigDecimal(pointEvaluateDO.getWaringDown())) < 0) {
            PointAlarmSaveReqVO pointAlarmSaveReqVO = createPointAlarm(localDateTime, pointDataDO);
            String content = value.compareTo(new BigDecimal(pointEvaluateDO.getWaringUp())) > 0 ? value + " > 异常报警上限值 " + pointEvaluateDO.getWaringUp() : value + " < 异常报警下限值 " + pointEvaluateDO.getWaringDown();
            pointAlarmSaveReqVO.setAlarmType(AlarmTypeEnum.ABNORMAL_ALARM.getType());
            pointAlarmSaveReqVO.setAlarmContent(content);
            log.info("pointId {} create alarm {}", pointDataDO.getPointId(), content);
            pointAlarmService.createPointAlarm(pointAlarmSaveReqVO);
        }
    }

    @NotNull
    private PointAlarmSaveReqVO createPointAlarm(LocalDateTime localDateTime, PointDataDO pointDataDO) {
        PointAlarmSaveReqVO pointAlarmSaveReqVO = new PointAlarmSaveReqVO();
        pointAlarmSaveReqVO.setProjectId(pointDataDO.getProjectId());
        pointAlarmSaveReqVO.setInstrumentModelId(pointDataDO.getInstrumentModelId());
        pointAlarmSaveReqVO.setPointId(pointDataDO.getPointId());
        pointAlarmSaveReqVO.setThingIdentity(pointDataDO.getThingIdentity());
        pointAlarmSaveReqVO.setThingName(pointDataDO.getThingName());
        pointAlarmSaveReqVO.setPointData(pointDataDO.getThingValue().toString());
        pointAlarmSaveReqVO.setPointTime(localDateTime);
        pointAlarmSaveReqVO.setAlarmTime(LocalDateTime.now());
        return pointAlarmSaveReqVO;
    }

    @Override
    public void insertBatch(List<PointDataDO> pointDataDOList) {
        pointDataMapper.insertBatch(pointDataDOList);
    }

    @Override
    public void deleteByPointIdAndPointTime(Long pointId, LocalDateTime pointTime) {
        pointDataMapper.delete(new LambdaQueryWrapperX<PointDataDO>().eq(PointDataDO::getPointId, pointId).eq(PointDataDO::getPointTime, pointTime));
    }

    @Override
    public List<PointDataDO> selectByPointIdAndTime(Long pointId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType) {
        return pointDataMapper.selectList(new LambdaQueryWrapperX<PointDataDO>().eq(PointDataDO::getPointId, pointId).between(PointDataDO::getPointTime, startTime, endTime)
                .eq(PointDataDO::getDataType, dataType));
    }

    @Override
    public PointDataDO getExtremaPointData(Long pointId, Long instrumentModelId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType, boolean isMax) {
        LambdaQueryWrapper<PointDataDO> queryWrapper = new LambdaQueryWrapperX<PointDataDO>().eq(PointDataDO::getPointId, pointId).eq(PointDataDO::getInstrumentModelId, instrumentModelId)
                .between(PointDataDO::getPointTime, startTime, endTime);
        if (Objects.nonNull(dataType)) {
            queryWrapper.eq(PointDataDO::getDataType, dataType);
        }
        if (isMax) {
            queryWrapper.orderByDesc(PointDataDO::getThingValue);
        } else {
            queryWrapper.orderByAsc(PointDataDO::getThingValue);
        }
        queryWrapper.last("limit 1");
        return pointDataMapper.selectOne(queryWrapper);
    }

    /**
     * 首条测值
     *
     * @param pointId
     * @param instrumentModelId
     * @return
     */
    private PointDataDO getFirstPointData(Long pointId, Long instrumentModelId) {
        return pointDataMapper.selectOne(new LambdaQueryWrapperX<PointDataDO>()
                .eq(PointDataDO::getPointId, pointId).eq(PointDataDO::getInstrumentModelId, instrumentModelId).orderByAsc(PointDataDO::getPointTime)
                .last("limit 1"));
    }

    /**
     * 相对测值：之前的第 n 条数据
     *
     * @param pointId
     * @param instrumentModelId
     * @param number
     * @return
     */
    private PointDataDO getPointData(Long pointId, Long instrumentModelId, LocalDateTime pointTime, Integer number) {
        List<PointDataDO> pointDataDOList = pointDataMapper.selectList(new LambdaQueryWrapperX<PointDataDO>()
                .eq(PointDataDO::getPointId, pointId).eq(PointDataDO::getInstrumentModelId, instrumentModelId)
                //小于等于
                .le(PointDataDO::getPointTime, pointTime)
                .orderByDesc(PointDataDO::getPointTime)
                .last("limit " + number));
        if (CollectionUtil.isNotEmpty(pointDataDOList)) {
            return pointDataDOList.get(0);
        }
        return null;
    }

    /**
     * 时间范围内测值集合
     *
     * @param pointId
     * @param instrumentModelId
     * @param startTime
     * @param endTime
     * @return
     */
    private List<PointDataDO> getPointDataByTime(Long pointId, Long instrumentModelId, LocalDateTime startTime, LocalDateTime endTime) {
        List<PointDataDO> pointDataDOList = pointDataMapper.selectList(new LambdaQueryWrapperX<PointDataDO>()
                .eq(PointDataDO::getPointId, pointId).eq(PointDataDO::getInstrumentModelId, instrumentModelId)
                .between(PointDataDO::getPointTime, startTime, endTime));
        return pointDataDOList;
    }


    /**
     * 时间范围内最接近的值
     *
     * @param pointDataDOList
     * @param targetTime
     * @return
     */
    private PointDataDO findClosestTime(List<PointDataDO> pointDataDOList, LocalDateTime targetTime) {
        PointDataDO closestTime = null;
        Duration shortestDuration = null;
        for (PointDataDO pointDataDO : pointDataDOList) {
            Duration duration = Duration.between(targetTime, pointDataDO.getPointTime());
            if (shortestDuration == null || duration.abs().compareTo(shortestDuration.abs()) < 0) {
                shortestDuration = duration;
                closestTime = pointDataDO;
            }
        }
        return closestTime;
    }

}
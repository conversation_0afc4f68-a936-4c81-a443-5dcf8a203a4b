package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-过程线--x轴--轴线
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-过程线-x轴-轴线")
@Data
public class PointDataProcessLineXaxisLineVO {

    private Boolean onZero;

    private Boolean show;
}

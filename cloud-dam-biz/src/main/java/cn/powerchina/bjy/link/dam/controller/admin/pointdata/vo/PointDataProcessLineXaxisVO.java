package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 监测图形-过程线--x轴
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-过程线-x轴")
@Data
public class PointDataProcessLineXaxisVO {

    private String type;

    private Boolean boundaryGap;

    private PointDataProcessLineAxisLabelVO axisLabel;

    private List<LocalDateTime> data;

    private PointDataProcessLineXaxisLineVO axisLine;

    private String position;
}

package cn.powerchina.bjy.link.dam.controller.admin.pointdata.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 测点数据json
 * @Author: yang<PERSON>gtao
 * @CreateDate: 2025/06/27
 */
@Data
public class PointDataJsonBO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 测点id
     */
    private Long pointId;

    /**
     * 监测时间
     */
    private LocalDateTime pointTime;

    /**
     * 监测时间组
     * 如果最大间隔是日，那么同一测点同一分量，同一天的数据要在一组
     * 如果最大间隔是小时，那么同一测点同一分量，同一小时的数据要在一组
     */
    private LocalDateTime pointTimeGroup;

    /**
     * 仪器类型id
     */
    private Long instrumentId;

    /**
     * 分量id
     */
    private Long instrumentModelId;

    /**
     * 分量名称
     */
    private String thingName;

    /**
     * 分量标识符
     */
    private String thingIdentity;

    /**
     * 分量值
     */
    private BigDecimal thingValue;

    /**
     * 采集类型(1：自动化采集，2：人工录入）
     */
    private Integer dataType;

    /**
     * 数据状态(1：正常数据，0：异常数据）
     */
    private Integer dataStatus;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 审核状态
     */
    private Integer reviewStatus;

    /**
     * 审核人
     */
    private String reviewer;

    /**
     * 审核人名称
     */
    private String reviewName;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 回滚测点数据导入id
     */
    private Long rollbackImportId;
}

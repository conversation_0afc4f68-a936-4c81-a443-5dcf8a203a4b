package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 监测图形-分布图-测点信息-测点值
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图-测点信息-测点值")
@Data
public class PointDataResVO {

    @Schema(description = "分量值")
    private BigDecimal thingValue;

    @Schema(description = "测点名称")
    private String pointName;
}
